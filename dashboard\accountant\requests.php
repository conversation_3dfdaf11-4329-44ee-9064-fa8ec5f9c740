<?php
/**
 * Accountant Requests Management
 * إدارة طلبات المحاسب
 */

require_once '../../backend/config/config.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn()) {
    redirect(getUrl('login.php'));
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'accountant') {
    redirect(getUrl('login.php'));
}

// معالجة تحديث حالة الطلب
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $request_id = (int)$_POST['request_id'];
    $new_status = sanitize($_POST['status']);
    $notes = sanitize($_POST['notes'] ?? '');
    
    // التحقق من أن الطلب معين للمحاسب الحالي
    $request = fetchOne(
        "SELECT * FROM service_requests WHERE id = :id AND assigned_accountant_id = :accountant_id",
        ['id' => $request_id, 'accountant_id' => $user['id']]
    );
    
    if ($request) {
        try {
            $sql = "UPDATE service_requests SET status = :status, notes = :notes, updated_at = NOW() WHERE id = :id";
            executeQuery($sql, [
                'status' => $new_status,
                'notes' => $notes,
                'id' => $request_id
            ]);
            
            // إذا تم إكمال الطلب، تحديث تاريخ الإكمال
            if ($new_status === 'completed') {
                $sql = "UPDATE service_requests SET actual_completion = NOW() WHERE id = :id";
                executeQuery($sql, ['id' => $request_id]);
            }
            
            showMessage('تم تحديث حالة الطلب بنجاح', 'success');
            redirect(getUrl('dashboard/accountant/requests.php'));
        } catch (Exception $e) {
            showMessage('حدث خطأ أثناء تحديث الطلب', 'error');
            logError("Update request status error: " . $e->getMessage());
        }
    } else {
        showMessage('الطلب غير موجود أو غير معين لك', 'error');
    }
}

// فلترة الطلبات
$status_filter = $_GET['status'] ?? 'all';
$search = sanitize($_GET['search'] ?? '');

// بناء استعلام الطلبات
$where_conditions = ["sr.assigned_accountant_id = :accountant_id"];
$params = ['accountant_id' => $user['id']];

if ($status_filter !== 'all') {
    $where_conditions[] = "sr.status = :status";
    $params['status'] = $status_filter;
}

if (!empty($search)) {
    $where_conditions[] = "(sr.request_title LIKE :search OR u.full_name LIKE :search OR s.service_name_ar LIKE :search)";
    $params['search'] = "%$search%";
}

$where_clause = implode(' AND ', $where_conditions);

// الحصول على الطلبات
$requests = [];
try {
    $sql = "SELECT sr.*, s.service_name_ar, u.full_name as client_name, u.email as client_email, u.phone as client_phone
            FROM service_requests sr 
            LEFT JOIN services s ON sr.service_id = s.id 
            LEFT JOIN users u ON sr.user_id = u.id 
            WHERE $where_clause 
            ORDER BY sr.created_at DESC";
    
    $requests = fetchAll($sql, $params);
} catch (Exception $e) {
    logError("Fetch requests error: " . $e->getMessage());
}

// إحصائيات سريعة
$stats = [
    'all' => 0,
    'pending' => 0,
    'in_progress' => 0,
    'under_review' => 0,
    'completed' => 0
];

try {
    foreach (['all', 'pending', 'in_progress', 'under_review', 'completed'] as $status) {
        if ($status === 'all') {
            $count_sql = "SELECT COUNT(*) as count FROM service_requests WHERE assigned_accountant_id = :accountant_id";
            $count_params = ['accountant_id' => $user['id']];
        } else {
            $count_sql = "SELECT COUNT(*) as count FROM service_requests WHERE assigned_accountant_id = :accountant_id AND status = :status";
            $count_params = ['accountant_id' => $user['id'], 'status' => $status];
        }
        $stats[$status] = fetchOne($count_sql, $count_params)['count'];
    }
} catch (Exception $e) {
    logError("Stats error: " . $e->getMessage());
}

// الحصول على رسالة الفلاش
$flash_message = getFlashMessage();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الطلبات - <?php echo SITE_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'muted-green': '#CAD2C5',
                        'warm-beige': '#FFE5B4',
                        'earth-green': '#52796F',
                        'sand-brown': '#8D7B68',
                        'light-gray': '#F0F0F0',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-cairo bg-light-gray">
    
    <!-- Navigation -->
    <?php include '../../frontend/components/navbar.php'; ?>
    
    <div class="container mx-auto px-4 py-8">
        
        <!-- Flash Messages -->
        <?php if ($flash_message): ?>
            <div class="mb-6 p-4 rounded-lg <?php echo $flash_message['type'] === 'success' ? 'bg-green-100 text-green-700 border border-green-300' : 'bg-red-100 text-red-700 border border-red-300'; ?>">
                <div class="flex items-center">
                    <i class="fas <?php echo $flash_message['type'] === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> ml-2"></i>
                    <?php echo htmlspecialchars($flash_message['message']); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-earth-green mb-2">إدارة الطلبات</h1>
                    <p class="text-gray-600">عرض وإدارة الطلبات المعينة لك</p>
                </div>
                <a href="<?php echo getUrl('dashboard/accountant'); ?>" 
                   class="bg-earth-green text-white px-4 py-2 rounded-lg hover:bg-sand-brown transition-colors">
                    <i class="fas fa-arrow-right ml-2"></i>
                    العودة للوحة التحكم
                </a>
            </div>
        </div>
        
        <!-- Statistics -->
        <div class="grid md:grid-cols-5 gap-4 mb-8">
            <div class="bg-white rounded-lg shadow p-4 text-center">
                <div class="text-2xl font-bold text-earth-green"><?php echo $stats['all']; ?></div>
                <div class="text-sm text-gray-600">إجمالي الطلبات</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 text-center">
                <div class="text-2xl font-bold text-yellow-600"><?php echo $stats['pending']; ?></div>
                <div class="text-sm text-gray-600">معلقة</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 text-center">
                <div class="text-2xl font-bold text-blue-600"><?php echo $stats['in_progress']; ?></div>
                <div class="text-sm text-gray-600">قيد التنفيذ</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 text-center">
                <div class="text-2xl font-bold text-purple-600"><?php echo $stats['under_review']; ?></div>
                <div class="text-sm text-gray-600">قيد المراجعة</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 text-center">
                <div class="text-2xl font-bold text-green-600"><?php echo $stats['completed']; ?></div>
                <div class="text-sm text-gray-600">مكتملة</div>
            </div>
        </div>
        
        <!-- Filters -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <form method="GET" class="flex flex-wrap items-center gap-4">
                
                <!-- Status Filter -->
                <div class="flex-1 min-w-48">
                    <label class="block text-sm font-medium text-gray-700 mb-2">فلترة حسب الحالة</label>
                    <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green">
                        <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>جميع الطلبات</option>
                        <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>معلقة</option>
                        <option value="in_progress" <?php echo $status_filter === 'in_progress' ? 'selected' : ''; ?>>قيد التنفيذ</option>
                        <option value="under_review" <?php echo $status_filter === 'under_review' ? 'selected' : ''; ?>>قيد المراجعة</option>
                        <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>مكتملة</option>
                    </select>
                </div>
                
                <!-- Search -->
                <div class="flex-1 min-w-48">
                    <label class="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                    <input type="text" 
                           name="search" 
                           value="<?php echo htmlspecialchars($search); ?>"
                           placeholder="البحث في العنوان أو اسم العميل أو الخدمة..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green">
                </div>
                
                <!-- Submit -->
                <div class="flex items-end">
                    <button type="submit" 
                            class="bg-earth-green text-white px-6 py-2 rounded-lg hover:bg-sand-brown transition-colors">
                        <i class="fas fa-search ml-2"></i>
                        بحث
                    </button>
                </div>
                
            </form>
        </div>

        <!-- Requests Table -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-earth-green">
                    الطلبات المعينة (<?php echo count($requests); ?>)
                </h2>
            </div>

            <?php if (empty($requests)): ?>
                <div class="text-center py-12">
                    <i class="fas fa-inbox text-6xl text-gray-400 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">لا توجد طلبات</h3>
                    <p class="text-gray-500">لا توجد طلبات تطابق معايير البحث المحددة</p>
                </div>
            <?php else: ?>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الطلب</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">العميل</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الخدمة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التاريخ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($requests as $request): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4">
                                        <div class="text-sm font-medium text-gray-900">
                                            <?php echo htmlspecialchars($request['request_title']); ?>
                                        </div>
                                        <?php if (!empty($request['description'])): ?>
                                            <div class="text-sm text-gray-500 mt-1">
                                                <?php echo htmlspecialchars(substr($request['description'], 0, 100)) . (strlen($request['description']) > 100 ? '...' : ''); ?>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm font-medium text-gray-900">
                                            <?php echo htmlspecialchars($request['client_name'] ?? 'غير محدد'); ?>
                                        </div>
                                        <?php if (!empty($request['client_email'])): ?>
                                            <div class="text-sm text-gray-500">
                                                <?php echo htmlspecialchars($request['client_email']); ?>
                                            </div>
                                        <?php endif; ?>
                                        <?php if (!empty($request['client_phone'])): ?>
                                            <div class="text-sm text-gray-500">
                                                <?php echo htmlspecialchars($request['client_phone']); ?>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-900">
                                        <?php echo htmlspecialchars($request['service_name_ar'] ?? 'خدمة محاسبية'); ?>
                                    </td>
                                    <td class="px-6 py-4">
                                        <?php
                                        $status_classes = [
                                            'pending' => 'bg-yellow-100 text-yellow-800',
                                            'in_progress' => 'bg-blue-100 text-blue-800',
                                            'under_review' => 'bg-purple-100 text-purple-800',
                                            'completed' => 'bg-green-100 text-green-800',
                                            'cancelled' => 'bg-red-100 text-red-800'
                                        ];

                                        $status_text = [
                                            'pending' => 'معلق',
                                            'in_progress' => 'قيد التنفيذ',
                                            'under_review' => 'قيد المراجعة',
                                            'completed' => 'مكتمل',
                                            'cancelled' => 'ملغي'
                                        ];
                                        ?>
                                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $status_classes[$request['status']] ?? 'bg-gray-100 text-gray-800'; ?>">
                                            <?php echo $status_text[$request['status']] ?? $request['status']; ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-900">
                                        <?php echo formatCurrency($request['total_amount']); ?>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-500">
                                        <?php echo formatArabicDate($request['created_at']); ?>
                                    </td>
                                    <td class="px-6 py-4 text-sm font-medium">
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <!-- View Details -->
                                            <a href="<?php echo getUrl('dashboard/accountant/request-details.php?id=' . $request['id']); ?>"
                                               class="text-earth-green hover:text-sand-brown transition-colors"
                                               title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>

                                            <!-- Update Status -->
                                            <button onclick="openStatusModal(<?php echo $request['id']; ?>, '<?php echo $request['status']; ?>', '<?php echo htmlspecialchars($request['notes'] ?? '', ENT_QUOTES); ?>')"
                                                    class="text-blue-600 hover:text-blue-900 transition-colors"
                                                    title="تحديث الحالة">
                                                <i class="fas fa-edit"></i>
                                            </button>

                                            <!-- View Documents -->
                                            <a href="<?php echo getUrl('dashboard/accountant/documents.php?request_id=' . $request['id']); ?>"
                                               class="text-purple-600 hover:text-purple-900 transition-colors"
                                               title="عرض المستندات">
                                                <i class="fas fa-folder"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Status Update Modal -->
    <div id="statusModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">تحديث حالة الطلب</h3>
                        <button onclick="closeStatusModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <form method="POST" id="statusForm">
                        <input type="hidden" name="update_status" value="1">
                        <input type="hidden" name="request_id" id="modalRequestId">

                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">الحالة الجديدة</label>
                            <select name="status" id="modalStatus" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green">
                                <option value="pending">معلق</option>
                                <option value="in_progress">قيد التنفيذ</option>
                                <option value="under_review">قيد المراجعة</option>
                                <option value="completed">مكتمل</option>
                            </select>
                        </div>

                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                            <textarea name="notes"
                                      id="modalNotes"
                                      rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green"
                                      placeholder="أضف ملاحظات حول تحديث الحالة..."></textarea>
                        </div>

                        <div class="flex justify-end space-x-3 space-x-reverse">
                            <button type="button"
                                    onclick="closeStatusModal()"
                                    class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">
                                إلغاء
                            </button>
                            <button type="submit"
                                    class="px-4 py-2 bg-earth-green text-white rounded-lg hover:bg-sand-brown transition-colors">
                                تحديث الحالة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include '../../frontend/components/footer.php'; ?>

    <!-- Scripts -->
    <script src="../../assets/js/main.js"></script>

    <script>
        function openStatusModal(requestId, currentStatus, currentNotes) {
            document.getElementById('modalRequestId').value = requestId;
            document.getElementById('modalStatus').value = currentStatus;
            document.getElementById('modalNotes').value = currentNotes;
            document.getElementById('statusModal').classList.remove('hidden');
        }

        function closeStatusModal() {
            document.getElementById('statusModal').classList.add('hidden');
        }

        // Close modal when clicking outside
        document.getElementById('statusModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeStatusModal();
            }
        });
    </script>
</body>
</html>
