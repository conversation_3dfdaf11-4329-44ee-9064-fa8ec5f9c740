-- قاعدة بيانات محاسبك الرقمي
-- Digital Accountant Database

CREATE DATABASE IF NOT EXISTS mohassebk_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE mohassebk_db;

-- جدول المستخدمين (Users Table)
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    role ENUM('client', 'accountant', 'admin') DEFAULT 'client',
    company_name VARCHAR(100),
    tax_number VARCHAR(50),
    address TEXT,
    profile_image VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الخدمات المحاسبية (Accounting Services Table)
CREATE TABLE services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    service_name VARCHAR(100) NOT NULL,
    service_name_ar VARCHAR(100) NOT NULL,
    description TEXT,
    description_ar TEXT,
    base_price DECIMAL(10,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول طلبات الخدمات (Service Requests Table)
CREATE TABLE service_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    service_id INT NOT NULL,
    request_title VARCHAR(200) NOT NULL,
    description TEXT,
    status ENUM('pending', 'in_progress', 'under_review', 'completed', 'cancelled') DEFAULT 'pending',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    assigned_accountant_id INT,
    estimated_completion DATE,
    actual_completion DATE,
    total_amount DECIMAL(10,2) DEFAULT 0.00,
    payment_status ENUM('pending', 'paid', 'refunded') DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id),
    FOREIGN KEY (assigned_accountant_id) REFERENCES users(id)
);

-- جدول المستندات المرفوعة (Uploaded Documents Table)
CREATE TABLE documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    request_id INT NOT NULL,
    user_id INT NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    stored_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_processed BOOLEAN DEFAULT FALSE,
    ai_analysis TEXT,
    FOREIGN KEY (request_id) REFERENCES service_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول الرسائل والتواصل (Messages Table)
CREATE TABLE messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    request_id INT NOT NULL,
    sender_id INT NOT NULL,
    receiver_id INT NOT NULL,
    message_text TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (request_id) REFERENCES service_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول الإشعارات (Notifications Table)
CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    related_request_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (related_request_id) REFERENCES service_requests(id) ON DELETE SET NULL
);

-- جدول المدفوعات الوهمية (Mock Payments Table)
CREATE TABLE payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    request_id INT NOT NULL,
    user_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('credit_card', 'bank_transfer', 'paypal', 'cash') DEFAULT 'credit_card',
    transaction_id VARCHAR(100) UNIQUE,
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    FOREIGN KEY (request_id) REFERENCES service_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول إعدادات النظام (System Settings Table)
CREATE TABLE system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description_ar VARCHAR(255),
    description_en VARCHAR(255),
    is_editable BOOLEAN DEFAULT TRUE,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- إدراج الخدمات الأساسية
INSERT INTO services (service_name, service_name_ar, description_ar, base_price) VALUES
('Tax Calculation', 'حساب الضرائب', 'خدمة حساب الضرائب المستحقة على الشركات والأفراد', 500.00),
('Financial Audit', 'المراجعة والتدقيق المالي', 'مراجعة وتدقيق القوائم المالية والحسابات', 1500.00),
('Financial Statements', 'إعداد القوائم المالية', 'إعداد القوائم المالية الشاملة للشركات', 1000.00),
('Account Reconciliation', 'تسوية الحسابات المالية', 'تسوية ومطابقة الحسابات المالية', 750.00),
('Profit & Tax Management', 'إدارة الأرباح والضرائب', 'إدارة وتخطيط الأرباح والضرائب', 800.00),
('Debt Management', 'إدارة الديون', 'إدارة ومتابعة الديون والمستحقات', 600.00),
('Financial Consulting', 'خدمات الاستشارة المالية', 'استشارات مالية متخصصة للشركات', 1200.00);

-- إدراج إعدادات النظام الأساسية
INSERT INTO system_settings (setting_key, setting_value, setting_type, description_ar, description_en) VALUES
('site_name', 'محاسبك الرقمي', 'string', 'اسم الموقع', 'Site Name'),
('site_email', '<EMAIL>', 'string', 'البريد الإلكتروني للموقع', 'Site Email'),
('site_phone', '+************', 'string', 'رقم هاتف الموقع', 'Site Phone'),
('max_file_size', '10485760', 'number', 'الحد الأقصى لحجم الملف (بايت)', 'Maximum File Size (bytes)'),
('allowed_file_types', 'pdf,doc,docx,xls,xlsx,jpg,jpeg,png', 'string', 'أنواع الملفات المسموحة', 'Allowed File Types'),
('openrouter_api_key', '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', 'string', 'مفتاح API للذكاء الاصطناعي', 'OpenRouter API Key'),
('ai_model', 'deepseek/deepseek-chat:free', 'string', 'نموذج الذكاء الاصطناعي', 'AI Model');

-- إنشاء مستخدم إداري افتراضي
INSERT INTO users (username, email, password, full_name, role, is_active, email_verified) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'admin', TRUE, TRUE);

-- إنشاء محاسب تجريبي
INSERT INTO users (username, email, password, full_name, role, is_active, email_verified) VALUES
('accountant1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'أحمد المحاسب', 'accountant', TRUE, TRUE);

-- جدول رسائل التواصل من صفحة اتصل بنا (Contact Messages Table)
CREATE TABLE contact_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    status ENUM('new', 'read', 'replied', 'closed') DEFAULT 'new',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
