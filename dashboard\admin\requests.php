<?php
/**
 * Admin/Accountant Requests Management
 * إدارة الطلبات للإدارة والمحاسبين
 */

require_once '../../backend/config/config.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn()) {
    redirect(getUrl('login.php'));
}

$user = getCurrentUser();
if (!$user || !in_array($user['role'], ['admin', 'accountant'])) {
    redirect(getUrl('login.php'));
}

// معاملات البحث والتصفية
$search = sanitize($_GET['search'] ?? '');
$status_filter = sanitize($_GET['status'] ?? '');
$priority_filter = sanitize($_GET['priority'] ?? '');
$assigned_filter = sanitize($_GET['assigned'] ?? '');
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 15;
$offset = ($page - 1) * $per_page;

// معالجة تحديث حالة الطلب
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $request_id = (int)($_POST['request_id'] ?? 0);
    $new_status = sanitize($_POST['status'] ?? '');
    $notes = sanitize($_POST['notes'] ?? '');
    
    if ($request_id && $new_status) {
        try {
            global $database;
            
            $update_data = [
                'status' => $new_status,
                'notes' => $notes
            ];
            
            // إذا كان المحاسب، تعيين نفسه للطلب
            if ($user['role'] === 'accountant' && $new_status === 'in_progress') {
                $update_data['assigned_accountant_id'] = $user['id'];
            }
            
            // إذا تم إكمال الطلب، تحديث تاريخ الإكمال
            if ($new_status === 'completed') {
                $update_data['actual_completion'] = date('Y-m-d');
            }
            
            $database->update('service_requests', $update_data, 'id = :id', ['id' => $request_id]);
            
            // إنشاء إشعار للعميل
            $request_info = fetchOne("SELECT user_id, request_title FROM service_requests WHERE id = :id", ['id' => $request_id]);
            if ($request_info) {
                $status_text = [
                    'pending' => 'معلق',
                    'in_progress' => 'قيد التنفيذ',
                    'under_review' => 'قيد المراجعة',
                    'completed' => 'مكتمل',
                    'cancelled' => 'ملغي'
                ];
                
                $notification_data = [
                    'user_id' => $request_info['user_id'],
                    'title' => 'تحديث حالة الطلب',
                    'message' => "تم تحديث حالة طلبك '{$request_info['request_title']}' إلى: {$status_text[$new_status]}",
                    'type' => $new_status === 'completed' ? 'success' : 'info',
                    'related_request_id' => $request_id
                ];
                $database->insert('notifications', $notification_data);
            }
            
            showMessage('تم تحديث حالة الطلب بنجاح', 'success');
            redirect(getUrl('dashboard/admin/requests.php'));
            
        } catch (Exception $e) {
            logError("Status update error: " . $e->getMessage());
            showMessage('حدث خطأ أثناء تحديث الحالة', 'error');
        }
    }
}

// بناء استعلام البحث
$where_conditions = ['1=1'];
$params = [];

if (!empty($search)) {
    $where_conditions[] = '(sr.request_title LIKE :search OR sr.description LIKE :search OR u.full_name LIKE :search OR s.service_name_ar LIKE :search)';
    $params['search'] = '%' . $search . '%';
}

if (!empty($status_filter)) {
    $where_conditions[] = 'sr.status = :status';
    $params['status'] = $status_filter;
}

if (!empty($priority_filter)) {
    $where_conditions[] = 'sr.priority = :priority';
    $params['priority'] = $priority_filter;
}

if (!empty($assigned_filter)) {
    if ($assigned_filter === 'unassigned') {
        $where_conditions[] = 'sr.assigned_accountant_id IS NULL';
    } elseif ($assigned_filter === 'me' && $user['role'] === 'accountant') {
        $where_conditions[] = 'sr.assigned_accountant_id = :my_id';
        $params['my_id'] = $user['id'];
    }
}

$where_clause = implode(' AND ', $where_conditions);

// الحصول على إجمالي عدد الطلبات
$total_requests = 0;
try {
    $count_sql = "SELECT COUNT(*) as count FROM service_requests sr 
                  LEFT JOIN users u ON sr.user_id = u.id 
                  LEFT JOIN services s ON sr.service_id = s.id 
                  WHERE $where_clause";
    $total_requests = fetchOne($count_sql, $params)['count'];
} catch (Exception $e) {
    logError("Requests count error: " . $e->getMessage());
}

$total_pages = ceil($total_requests / $per_page);

// الحصول على الطلبات
$requests = [];
try {
    $sql = "SELECT sr.*, s.service_name_ar, u.full_name as client_name, u.email as client_email,
                   acc.full_name as accountant_name
            FROM service_requests sr 
            LEFT JOIN services s ON sr.service_id = s.id 
            LEFT JOIN users u ON sr.user_id = u.id
            LEFT JOIN users acc ON sr.assigned_accountant_id = acc.id
            WHERE $where_clause 
            ORDER BY 
                CASE sr.priority 
                    WHEN 'urgent' THEN 1 
                    WHEN 'high' THEN 2 
                    WHEN 'medium' THEN 3 
                    WHEN 'low' THEN 4 
                END,
                sr.created_at DESC 
            LIMIT :limit OFFSET :offset";
    
    $stmt = executeQuery($sql, array_merge($params, ['limit' => $per_page, 'offset' => $offset]));
    $requests = $stmt->fetchAll();
} catch (Exception $e) {
    logError("Requests fetch error: " . $e->getMessage());
}

// الحصول على قائمة المحاسبين
$accountants = [];
if ($user['role'] === 'admin') {
    try {
        $accountants = fetchAll("SELECT id, full_name FROM users WHERE role IN ('admin', 'accountant') AND is_active = 1 ORDER BY full_name");
    } catch (Exception $e) {
        logError("Accountants fetch error: " . $e->getMessage());
    }
}

// الحصول على رسالة الفلاش
$flash_message = getFlashMessage();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الطلبات - <?php echo SITE_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'muted-green': '#CAD2C5',
                        'warm-beige': '#FFE5B4',
                        'earth-green': '#52796F',
                        'sand-brown': '#8D7B68',
                        'light-gray': '#F0F0F0',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-cairo bg-light-gray">
    
    <!-- Navigation -->
    <?php include '../../frontend/components/navbar.php'; ?>
    
    <div class="container mx-auto px-4 py-8">
        
        <!-- Flash Messages -->
        <?php if ($flash_message): ?>
            <div class="mb-6 p-4 rounded-lg <?php echo $flash_message['type'] === 'success' ? 'bg-green-100 text-green-700 border border-green-300' : 'bg-red-100 text-red-700 border border-red-300'; ?>">
                <div class="flex items-center">
                    <i class="fas <?php echo $flash_message['type'] === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> ml-2"></i>
                    <?php echo htmlspecialchars($flash_message['message']); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Header -->
        <div class="mb-8">
            <nav class="flex items-center space-x-2 space-x-reverse text-sm text-gray-600 mb-4">
                <a href="<?php echo getUrl(); ?>" class="hover:text-earth-green transition-colors">الرئيسية</a>
                <i class="fas fa-chevron-left text-xs"></i>
                <a href="<?php echo getUrl('dashboard/admin'); ?>" class="hover:text-earth-green transition-colors">لوحة التحكم</a>
                <i class="fas fa-chevron-left text-xs"></i>
                <span class="text-earth-green">إدارة الطلبات</span>
            </nav>
            
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-earth-green mb-2">
                        إدارة الطلبات
                    </h1>
                    <p class="text-gray-600">
                        متابعة ومعالجة طلبات العملاء المحاسبية
                    </p>
                </div>
                <div class="flex items-center space-x-3 space-x-reverse">
                    <span class="text-sm text-gray-600">
                        إجمالي الطلبات: <span class="font-semibold text-earth-green"><?php echo $total_requests; ?></span>
                    </span>
                </div>
            </div>
        </div>
        
        <!-- Search and Filter -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <form method="GET" class="grid md:grid-cols-5 gap-4">
                
                <!-- Search -->
                <div class="md:col-span-2">
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-2">
                        البحث
                    </label>
                    <div class="relative">
                        <input type="text" 
                               id="search" 
                               name="search" 
                               value="<?php echo htmlspecialchars($search); ?>"
                               class="w-full px-4 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors"
                               placeholder="ابحث في العنوان، العميل، أو نوع الخدمة...">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                    </div>
                </div>
                
                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                        الحالة
                    </label>
                    <select id="status" 
                            name="status"
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                        <option value="">جميع الحالات</option>
                        <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>معلق</option>
                        <option value="in_progress" <?php echo $status_filter === 'in_progress' ? 'selected' : ''; ?>>قيد التنفيذ</option>
                        <option value="under_review" <?php echo $status_filter === 'under_review' ? 'selected' : ''; ?>>قيد المراجعة</option>
                        <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>مكتمل</option>
                        <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>ملغي</option>
                    </select>
                </div>
                
                <!-- Priority Filter -->
                <div>
                    <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">
                        الأولوية
                    </label>
                    <select id="priority" 
                            name="priority"
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                        <option value="">جميع الأولويات</option>
                        <option value="urgent" <?php echo $priority_filter === 'urgent' ? 'selected' : ''; ?>>عاجلة</option>
                        <option value="high" <?php echo $priority_filter === 'high' ? 'selected' : ''; ?>>عالية</option>
                        <option value="medium" <?php echo $priority_filter === 'medium' ? 'selected' : ''; ?>>متوسطة</option>
                        <option value="low" <?php echo $priority_filter === 'low' ? 'selected' : ''; ?>>منخفضة</option>
                    </select>
                </div>
                
                <!-- Assignment Filter -->
                <div>
                    <label for="assigned" class="block text-sm font-medium text-gray-700 mb-2">
                        التعيين
                    </label>
                    <select id="assigned" 
                            name="assigned"
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                        <option value="">الكل</option>
                        <option value="unassigned" <?php echo $assigned_filter === 'unassigned' ? 'selected' : ''; ?>>غير معين</option>
                        <?php if ($user['role'] === 'accountant'): ?>
                            <option value="me" <?php echo $assigned_filter === 'me' ? 'selected' : ''; ?>>طلباتي</option>
                        <?php endif; ?>
                    </select>
                </div>
            </form>
            
            <div class="mt-4 flex items-center justify-between">
                <button onclick="document.querySelector('form').submit()" 
                        class="bg-earth-green text-white px-6 py-2 rounded-lg font-semibold hover:bg-sand-brown transition-all duration-300">
                    <i class="fas fa-filter ml-2"></i>
                    تصفية
                </button>
                
                <?php if (!empty($search) || !empty($status_filter) || !empty($priority_filter) || !empty($assigned_filter)): ?>
                    <a href="<?php echo getUrl('dashboard/admin/requests.php'); ?>" 
                       class="text-sm text-gray-600 hover:text-earth-green transition-colors">
                        <i class="fas fa-times ml-1"></i>
                        مسح التصفية
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Requests List -->
        <div class="bg-white rounded-lg shadow-lg">
            <div class="overflow-x-auto">
                <?php if (empty($requests)): ?>
                    <div class="text-center py-12">
                        <i class="fas fa-inbox text-4xl text-gray-400 mb-4"></i>
                        <p class="text-gray-600 mb-4">
                            <?php if (!empty($search) || !empty($status_filter) || !empty($priority_filter) || !empty($assigned_filter)): ?>
                                لا توجد طلبات تطابق معايير البحث
                            <?php else: ?>
                                لا توجد طلبات حتى الآن
                            <?php endif; ?>
                        </p>
                    </div>
                <?php else: ?>
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">العميل والطلب</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الخدمة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الأولوية</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المحاسب</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التاريخ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($requests as $request): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 bg-earth-green rounded-full flex items-center justify-center ml-3">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">
                                                    <?php echo htmlspecialchars($request['client_name']); ?>
                                                </div>
                                                <div class="text-sm text-gray-500">
                                                    <?php echo htmlspecialchars($request['client_email']); ?>
                                                </div>
                                                <div class="text-sm font-medium text-earth-green mt-1">
                                                    <?php echo htmlspecialchars($request['request_title']); ?>
                                                </div>
                                                <div class="text-xs text-gray-500">
                                                    #<?php echo $request['id']; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo htmlspecialchars($request['service_name_ar'] ?? 'خدمة محاسبية'); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php
                                        $status_classes = [
                                            'pending' => 'bg-yellow-100 text-yellow-800',
                                            'in_progress' => 'bg-blue-100 text-blue-800',
                                            'under_review' => 'bg-purple-100 text-purple-800',
                                            'completed' => 'bg-green-100 text-green-800',
                                            'cancelled' => 'bg-red-100 text-red-800'
                                        ];

                                        $status_text = [
                                            'pending' => 'معلق',
                                            'in_progress' => 'قيد التنفيذ',
                                            'under_review' => 'قيد المراجعة',
                                            'completed' => 'مكتمل',
                                            'cancelled' => 'ملغي'
                                        ];
                                        ?>
                                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $status_classes[$request['status']] ?? 'bg-gray-100 text-gray-800'; ?>">
                                            <?php echo $status_text[$request['status']] ?? $request['status']; ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php
                                        $priority_classes = [
                                            'low' => 'bg-gray-100 text-gray-800',
                                            'medium' => 'bg-blue-100 text-blue-800',
                                            'high' => 'bg-orange-100 text-orange-800',
                                            'urgent' => 'bg-red-100 text-red-800'
                                        ];

                                        $priority_text = [
                                            'low' => 'منخفضة',
                                            'medium' => 'متوسطة',
                                            'high' => 'عالية',
                                            'urgent' => 'عاجلة'
                                        ];
                                        ?>
                                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $priority_classes[$request['priority']] ?? 'bg-gray-100 text-gray-800'; ?>">
                                            <?php echo $priority_text[$request['priority']] ?? $request['priority']; ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php if ($request['accountant_name']): ?>
                                            <div class="flex items-center">
                                                <div class="w-6 h-6 bg-sand-brown rounded-full flex items-center justify-center ml-2">
                                                    <i class="fas fa-user text-white text-xs"></i>
                                                </div>
                                                <?php echo htmlspecialchars($request['accountant_name']); ?>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-gray-500">غير معين</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo number_format($request['total_amount'], 2); ?> ر.س
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <div><?php echo date('Y-m-d', strtotime($request['created_at'])); ?></div>
                                        <div class="text-xs"><?php echo date('H:i', strtotime($request['created_at'])); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <button onclick="openStatusModal(<?php echo $request['id']; ?>, '<?php echo $request['status']; ?>')"
                                                    class="text-blue-600 hover:text-blue-800 transition-colors"
                                                    title="تحديث الحالة">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <a href="<?php echo getUrl('dashboard/admin/request-details.php?id=' . $request['id']); ?>"
                                               class="text-earth-green hover:text-sand-brown transition-colors"
                                               title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo getUrl('dashboard/admin/documents.php?request_id=' . $request['id']); ?>"
                                               class="text-purple-600 hover:text-purple-800 transition-colors"
                                               title="المستندات">
                                                <i class="fas fa-folder"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Status Update Modal -->
    <div id="statusModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-earth-green mb-4">
                        تحديث حالة الطلب
                    </h3>

                    <form method="POST">
                        <input type="hidden" name="update_status" value="1">
                        <input type="hidden" name="request_id" id="modal_request_id">

                        <div class="mb-4">
                            <label for="modal_status" class="block text-sm font-medium text-gray-700 mb-2">
                                الحالة الجديدة
                            </label>
                            <select id="modal_status"
                                    name="status"
                                    required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                                <option value="pending">معلق</option>
                                <option value="in_progress">قيد التنفيذ</option>
                                <option value="under_review">قيد المراجعة</option>
                                <option value="completed">مكتمل</option>
                                <option value="cancelled">ملغي</option>
                            </select>
                        </div>

                        <div class="mb-6">
                            <label for="modal_notes" class="block text-sm font-medium text-gray-700 mb-2">
                                ملاحظات (اختياري)
                            </label>
                            <textarea id="modal_notes"
                                      name="notes"
                                      rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors"
                                      placeholder="أضف ملاحظات حول التحديث..."></textarea>
                        </div>

                        <div class="flex items-center justify-end space-x-3 space-x-reverse">
                            <button type="button"
                                    onclick="closeStatusModal()"
                                    class="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
                                إلغاء
                            </button>
                            <button type="submit"
                                    class="px-4 py-2 bg-earth-green text-white rounded-lg hover:bg-sand-brown transition-colors">
                                تحديث
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include '../../frontend/components/footer.php'; ?>

    <!-- Scripts -->
    <script src="../../assets/js/main.js"></script>

    <script>
        function openStatusModal(requestId, currentStatus) {
            document.getElementById('modal_request_id').value = requestId;
            document.getElementById('modal_status').value = currentStatus;
            document.getElementById('modal_notes').value = '';
            document.getElementById('statusModal').classList.remove('hidden');
        }

        function closeStatusModal() {
            document.getElementById('statusModal').classList.add('hidden');
        }

        // Close modal when clicking outside
        document.getElementById('statusModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeStatusModal();
            }
        });
    </script>
</body>
</html>
