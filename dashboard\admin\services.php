<?php
/**
 * Admin Service Management Page
 * صفحة إدارة الخدمات
 */

require_once '../../backend/config/config.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn()) {
    redirect(getUrl('login.php'));
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'admin') {
    redirect(getUrl('dashboard/client'));
}

// معالجة الإجراءات
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = sanitize($_POST['action']);
        
        try {
            global $database;
            
            switch ($action) {
                case 'add_service':
                    $service_data = [
                        'service_name_ar' => sanitize($_POST['service_name_ar']),
                        'service_name_en' => sanitize($_POST['service_name_en']),
                        'description_ar' => sanitize($_POST['description_ar']),
                        'description_en' => sanitize($_POST['description_en']),
                        'base_price' => (float)($_POST['base_price'] ?? 0),
                        'is_active' => isset($_POST['is_active']) ? 1 : 0,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    
                    if (empty($service_data['service_name_ar']) || empty($service_data['description_ar'])) {
                        $error_message = 'يرجى ملء جميع الحقول المطلوبة';
                    } else {
                        $database->insert('services', $service_data);
                        $success_message = 'تم إضافة الخدمة بنجاح';
                    }
                    break;
                    
                case 'edit_service':
                    $service_id = (int)($_POST['service_id'] ?? 0);
                    $service_data = [
                        'service_name_ar' => sanitize($_POST['service_name_ar']),
                        'service_name_en' => sanitize($_POST['service_name_en']),
                        'description_ar' => sanitize($_POST['description_ar']),
                        'description_en' => sanitize($_POST['description_en']),
                        'base_price' => (float)($_POST['base_price'] ?? 0),
                        'is_active' => isset($_POST['is_active']) ? 1 : 0,
                        'updated_at' => date('Y-m-d H:i:s')
                    ];
                    
                    if (empty($service_data['service_name_ar']) || empty($service_data['description_ar'])) {
                        $error_message = 'يرجى ملء جميع الحقول المطلوبة';
                    } else {
                        $database->update('services', $service_data, 'id = :id', ['id' => $service_id]);
                        $success_message = 'تم تحديث الخدمة بنجاح';
                    }
                    break;
                    
                case 'toggle_service':
                    $service_id = (int)($_POST['service_id'] ?? 0);
                    $current_status = fetchOne("SELECT is_active FROM services WHERE id = :id", ['id' => $service_id]);
                    $new_status = $current_status['is_active'] ? 0 : 1;
                    $database->update('services', ['is_active' => $new_status], 'id = :id', ['id' => $service_id]);
                    $success_message = $new_status ? 'تم تفعيل الخدمة بنجاح' : 'تم إلغاء تفعيل الخدمة بنجاح';
                    break;
                    
                case 'delete_service':
                    $service_id = (int)($_POST['service_id'] ?? 0);
                    // التحقق من وجود طلبات مرتبطة بالخدمة
                    $requests_count = fetchOne("SELECT COUNT(*) as count FROM service_requests WHERE service_id = :id", ['id' => $service_id])['count'];
                    
                    if ($requests_count > 0) {
                        $error_message = 'لا يمكن حذف هذه الخدمة لوجود طلبات مرتبطة بها';
                    } else {
                        $database->delete('services', 'id = :id', ['id' => $service_id]);
                        $success_message = 'تم حذف الخدمة بنجاح';
                    }
                    break;
            }
        } catch (Exception $e) {
            $error_message = 'حدث خطأ أثناء تنفيذ العملية';
            logError("Service management error: " . $e->getMessage());
        }
    }
}

// معاملات البحث والتصفية
$search = sanitize($_GET['search'] ?? '');
$status_filter = sanitize($_GET['status'] ?? '');
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 10;
$offset = ($page - 1) * $per_page;

// بناء استعلام البحث
$where_conditions = ['1=1'];
$params = [];

if (!empty($search)) {
    $where_conditions[] = '(service_name_ar LIKE :search OR service_name_en LIKE :search OR description_ar LIKE :search)';
    $params['search'] = '%' . $search . '%';
}

if (!empty($status_filter)) {
    $where_conditions[] = 'is_active = :status';
    $params['status'] = $status_filter === 'active' ? 1 : 0;
}

$where_clause = implode(' AND ', $where_conditions);

// الحصول على إجمالي عدد الخدمات
$total_services = 0;
try {
    $count_sql = "SELECT COUNT(*) as count FROM services WHERE $where_clause";
    $total_services = fetchOne($count_sql, $params)['count'];
} catch (Exception $e) {
    logError("Services count error: " . $e->getMessage());
}

$total_pages = ceil($total_services / $per_page);

// الحصول على الخدمات
$services = [];
try {
    $sql = "SELECT s.*, 
                   COUNT(sr.id) as request_count,
                   AVG(sr.total_amount) as avg_amount
            FROM services s 
            LEFT JOIN service_requests sr ON s.id = sr.service_id 
            WHERE $where_clause 
            GROUP BY s.id 
            ORDER BY s.created_at DESC 
            LIMIT :limit OFFSET :offset";
    
    $stmt = executeQuery($sql, array_merge($params, ['limit' => $per_page, 'offset' => $offset]));
    $services = $stmt->fetchAll();
} catch (Exception $e) {
    logError("Services fetch error: " . $e->getMessage());
}

// إحصائيات الخدمات
$service_stats = [
    'total' => 0,
    'active' => 0,
    'inactive' => 0,
    'total_requests' => 0,
    'avg_price' => 0
];

try {
    $stats_sql = "SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active,
                    SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as inactive,
                    AVG(base_price) as avg_price
                  FROM services";
    
    $service_stats = fetchOne($stats_sql);
    
    // إجمالي الطلبات
    $requests_stats = fetchOne("SELECT COUNT(*) as total_requests FROM service_requests");
    $service_stats['total_requests'] = $requests_stats['total_requests'];
    
} catch (Exception $e) {
    logError("Service stats error: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الخدمات - <?php echo SITE_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'muted-green': '#CAD2C5',
                        'warm-beige': '#FFE5B4',
                        'earth-green': '#52796F',
                        'sand-brown': '#8D7B68',
                        'light-gray': '#F0F0F0',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-cairo bg-light-gray">
    
    <!-- Navigation -->
    <?php include '../../frontend/components/navbar.php'; ?>
    
    <div class="container mx-auto px-4 py-8">
        
        <!-- Flash Messages -->
        <?php if ($success_message): ?>
            <div class="mb-6 p-4 rounded-lg bg-green-100 text-green-700 border border-green-300">
                <div class="flex items-center">
                    <i class="fas fa-check-circle ml-2"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <?php if ($error_message): ?>
            <div class="mb-6 p-4 rounded-lg bg-red-100 text-red-700 border border-red-300">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle ml-2"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Header -->
        <div class="mb-8">
            <nav class="flex items-center space-x-2 space-x-reverse text-sm text-gray-600 mb-4">
                <a href="<?php echo getUrl(); ?>" class="hover:text-earth-green transition-colors">الرئيسية</a>
                <i class="fas fa-chevron-left text-xs"></i>
                <a href="<?php echo getUrl('dashboard/admin'); ?>" class="hover:text-earth-green transition-colors">لوحة الإدارة</a>
                <i class="fas fa-chevron-left text-xs"></i>
                <span class="text-earth-green">إدارة الخدمات</span>
            </nav>
            
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-earth-green mb-2">إدارة الخدمات</h1>
                    <p class="text-gray-600">عرض وإدارة جميع الخدمات المحاسبية</p>
                </div>
                <div class="mt-4 md:mt-0">
                    <button onclick="openAddServiceModal()" 
                            class="bg-earth-green text-white px-6 py-2 rounded-lg hover:bg-sand-brown transition-colors">
                        <i class="fas fa-plus ml-2"></i>
                        إضافة خدمة جديدة
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Service Statistics -->
        <div class="grid md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            
            <!-- Total Services -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">إجمالي الخدمات</p>
                        <p class="text-2xl font-bold text-earth-green"><?php echo $service_stats['total']; ?></p>
                    </div>
                    <div class="w-12 h-12 bg-earth-green rounded-lg flex items-center justify-center">
                        <i class="fas fa-cogs text-white text-xl"></i>
                    </div>
                </div>
            </div>
            
            <!-- Active Services -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">خدمات نشطة</p>
                        <p class="text-2xl font-bold text-green-600"><?php echo $service_stats['active']; ?></p>
                    </div>
                    <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-white text-xl"></i>
                    </div>
                </div>
            </div>
            
            <!-- Inactive Services -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">خدمات غير نشطة</p>
                        <p class="text-2xl font-bold text-red-600"><?php echo $service_stats['inactive']; ?></p>
                    </div>
                    <div class="w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-times-circle text-white text-xl"></i>
                    </div>
                </div>
            </div>
            
            <!-- Total Requests -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">إجمالي الطلبات</p>
                        <p class="text-2xl font-bold text-blue-600"><?php echo $service_stats['total_requests']; ?></p>
                    </div>
                    <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-file-alt text-white text-xl"></i>
                    </div>
                </div>
            </div>
            
            <!-- Average Price -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">متوسط السعر</p>
                        <p class="text-2xl font-bold text-purple-600"><?php echo number_format($service_stats['avg_price'] ?? 0, 0); ?></p>
                        <p class="text-xs text-gray-500">دينار</p>
                    </div>
                    <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-dollar-sign text-white text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Filter -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <form method="GET" class="grid md:grid-cols-3 gap-4">

                <!-- Search -->
                <div class="md:col-span-2">
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                    <div class="relative">
                        <input type="text"
                               id="search"
                               name="search"
                               value="<?php echo htmlspecialchars($search); ?>"
                               class="w-full px-4 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors"
                               placeholder="ابحث في اسم الخدمة أو الوصف...">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                    </div>
                </div>

                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                    <select id="status"
                            name="status"
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                        <option value="">جميع الحالات</option>
                        <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>نشط</option>
                        <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                    </select>
                </div>
            </form>

            <div class="mt-4 flex items-center justify-between">
                <button onclick="document.querySelector('form').submit()"
                        class="bg-earth-green text-white px-6 py-2 rounded-lg font-semibold hover:bg-sand-brown transition-all duration-300">
                    <i class="fas fa-filter ml-2"></i>
                    تصفية
                </button>

                <?php if (!empty($search) || !empty($status_filter)): ?>
                    <a href="<?php echo getUrl('dashboard/admin/services.php'); ?>"
                       class="text-sm text-gray-600 hover:text-earth-green transition-colors">
                        <i class="fas fa-times ml-1"></i>
                        مسح التصفية
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Services List -->
        <div class="bg-white rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-earth-green">
                    قائمة الخدمات (<?php echo $total_services; ?>)
                </h2>
            </div>

            <div class="overflow-x-auto">
                <?php if (empty($services)): ?>
                    <div class="text-center py-12">
                        <i class="fas fa-cogs text-4xl text-gray-400 mb-4"></i>
                        <p class="text-gray-600 mb-4">
                            <?php if (!empty($search) || !empty($status_filter)): ?>
                                لا توجد خدمات تطابق معايير البحث
                            <?php else: ?>
                                لا توجد خدمات مضافة حتى الآن
                            <?php endif; ?>
                        </p>
                        <button onclick="openAddServiceModal()"
                                class="bg-earth-green text-white px-6 py-2 rounded-lg hover:bg-sand-brown transition-colors">
                            إضافة خدمة جديدة
                        </button>
                    </div>
                <?php else: ?>
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الخدمة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">السعر الأساسي</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">عدد الطلبات</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">متوسط المبلغ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الإنشاء</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($services as $service): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">
                                                <?php echo htmlspecialchars($service['service_name_ar']); ?>
                                            </div>
                                            <?php if ($service['service_name_en']): ?>
                                                <div class="text-sm text-gray-500">
                                                    <?php echo htmlspecialchars($service['service_name_en']); ?>
                                                </div>
                                            <?php endif; ?>
                                            <div class="text-xs text-gray-400 mt-1">
                                                <?php echo mb_substr($service['description_ar'], 0, 100) . (mb_strlen($service['description_ar']) > 100 ? '...' : ''); ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-semibold text-gray-900">
                                            <?php echo formatCurrency($service['base_price']); ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <?php echo $service['request_count']; ?> طلب
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <?php echo $service['avg_amount'] ? formatCurrency($service['avg_amount']) : 'لا يوجد'; ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $service['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                            <?php echo $service['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <div><?php echo date('Y-m-d', strtotime($service['created_at'])); ?></div>
                                        <div class="text-xs"><?php echo date('H:i', strtotime($service['created_at'])); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <!-- Toggle Status -->
                                            <form method="POST" class="inline">
                                                <input type="hidden" name="action" value="toggle_service">
                                                <input type="hidden" name="service_id" value="<?php echo $service['id']; ?>">
                                                <button type="submit"
                                                        class="<?php echo $service['is_active'] ? 'text-red-600 hover:text-red-800' : 'text-green-600 hover:text-green-800'; ?> transition-colors"
                                                        title="<?php echo $service['is_active'] ? 'إلغاء تفعيل' : 'تفعيل'; ?>"
                                                        onclick="return confirm('هل أنت متأكد من تغيير حالة هذه الخدمة؟')">
                                                    <i class="fas <?php echo $service['is_active'] ? 'fa-ban' : 'fa-check'; ?>"></i>
                                                </button>
                                            </form>

                                            <!-- Edit Service -->
                                            <button onclick="editService(<?php echo htmlspecialchars(json_encode($service)); ?>)"
                                                    class="text-blue-600 hover:text-blue-800 transition-colors"
                                                    title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>

                                            <!-- Delete Service -->
                                            <form method="POST" class="inline">
                                                <input type="hidden" name="action" value="delete_service">
                                                <input type="hidden" name="service_id" value="<?php echo $service['id']; ?>">
                                                <button type="submit"
                                                        class="text-red-600 hover:text-red-800 transition-colors"
                                                        title="حذف"
                                                        onclick="return confirm('هل أنت متأكد من حذف هذه الخدمة؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Add/Edit Service Modal -->
    <div id="serviceModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 id="modalTitle" class="text-lg font-semibold text-earth-green">إضافة خدمة جديدة</h3>
                        <button onclick="closeServiceModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <form id="serviceForm" method="POST" class="space-y-6">
                        <input type="hidden" name="action" id="formAction" value="add_service">
                        <input type="hidden" name="service_id" id="serviceId">

                        <!-- Arabic Name -->
                        <div>
                            <label for="service_name_ar" class="block text-sm font-medium text-gray-700 mb-2">
                                اسم الخدمة (عربي) <span class="text-red-500">*</span>
                            </label>
                            <input type="text"
                                   id="service_name_ar"
                                   name="service_name_ar"
                                   required
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                        </div>

                        <!-- English Name -->
                        <div>
                            <label for="service_name_en" class="block text-sm font-medium text-gray-700 mb-2">
                                اسم الخدمة (إنجليزي)
                            </label>
                            <input type="text"
                                   id="service_name_en"
                                   name="service_name_en"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                        </div>

                        <!-- Arabic Description -->
                        <div>
                            <label for="description_ar" class="block text-sm font-medium text-gray-700 mb-2">
                                وصف الخدمة (عربي) <span class="text-red-500">*</span>
                            </label>
                            <textarea id="description_ar"
                                      name="description_ar"
                                      rows="4"
                                      required
                                      class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors resize-vertical"></textarea>
                        </div>

                        <!-- English Description -->
                        <div>
                            <label for="description_en" class="block text-sm font-medium text-gray-700 mb-2">
                                وصف الخدمة (إنجليزي)
                            </label>
                            <textarea id="description_en"
                                      name="description_en"
                                      rows="4"
                                      class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors resize-vertical"></textarea>
                        </div>

                        <!-- Base Price -->
                        <div>
                            <label for="base_price" class="block text-sm font-medium text-gray-700 mb-2">
                                السعر الأساسي (دينار جزائري)
                            </label>
                            <input type="number"
                                   id="base_price"
                                   name="base_price"
                                   min="0"
                                   step="0.01"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                        </div>

                        <!-- Active Status -->
                        <div class="flex items-center">
                            <input type="checkbox"
                                   id="is_active"
                                   name="is_active"
                                   checked
                                   class="rounded border-gray-300 text-earth-green focus:ring-earth-green">
                            <label for="is_active" class="mr-2 text-sm text-gray-700">
                                خدمة نشطة
                            </label>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex items-center justify-end space-x-4 space-x-reverse pt-6 border-t border-gray-200">
                            <button type="button"
                                    onclick="closeServiceModal()"
                                    class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                                إلغاء
                            </button>
                            <button type="submit"
                                    class="px-6 py-2 bg-earth-green text-white rounded-lg hover:bg-sand-brown transition-colors">
                                <span id="submitButtonText">إضافة الخدمة</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include '../../frontend/components/footer.php'; ?>

    <!-- Scripts -->
    <script src="../../assets/js/main.js"></script>

    <script>
        // Open add service modal
        function openAddServiceModal() {
            document.getElementById('modalTitle').textContent = 'إضافة خدمة جديدة';
            document.getElementById('formAction').value = 'add_service';
            document.getElementById('submitButtonText').textContent = 'إضافة الخدمة';
            document.getElementById('serviceForm').reset();
            document.getElementById('is_active').checked = true;
            document.getElementById('serviceModal').classList.remove('hidden');
        }

        // Edit service
        function editService(service) {
            document.getElementById('modalTitle').textContent = 'تعديل الخدمة';
            document.getElementById('formAction').value = 'edit_service';
            document.getElementById('submitButtonText').textContent = 'حفظ التغييرات';
            document.getElementById('serviceId').value = service.id;
            document.getElementById('service_name_ar').value = service.service_name_ar;
            document.getElementById('service_name_en').value = service.service_name_en || '';
            document.getElementById('description_ar').value = service.description_ar;
            document.getElementById('description_en').value = service.description_en || '';
            document.getElementById('base_price').value = service.base_price;
            document.getElementById('is_active').checked = service.is_active == 1;
            document.getElementById('serviceModal').classList.remove('hidden');
        }

        // Close service modal
        function closeServiceModal() {
            document.getElementById('serviceModal').classList.add('hidden');
        }

        // Close modal when clicking outside
        document.getElementById('serviceModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeServiceModal();
            }
        });
    </script>
</body>
</html>
