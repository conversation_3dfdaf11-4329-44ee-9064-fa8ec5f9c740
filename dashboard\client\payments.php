<?php
/**
 * Client Payment History
 * سجل المدفوعات للعميل
 */

require_once '../../backend/config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(getUrl('login.php'));
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'client') {
    redirect(getUrl('login.php'));
}

// معاملات البحث والتصفية
$search = sanitize($_GET['search'] ?? '');
$status_filter = sanitize($_GET['status'] ?? '');
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 10;
$offset = ($page - 1) * $per_page;

// بناء استعلام البحث
$where_conditions = ['p.user_id = :user_id'];
$params = ['user_id' => $user['id']];

if (!empty($search)) {
    $where_conditions[] = '(sr.request_title LIKE :search OR p.transaction_id LIKE :search)';
    $params['search'] = '%' . $search . '%';
}

if (!empty($status_filter)) {
    $where_conditions[] = 'p.payment_status = :status';
    $params['status'] = $status_filter;
}

$where_clause = implode(' AND ', $where_conditions);

// الحصول على إجمالي عدد المدفوعات
$total_payments = 0;
try {
    $count_sql = "SELECT COUNT(*) as count FROM payments p 
                  LEFT JOIN service_requests sr ON p.request_id = sr.id 
                  WHERE $where_clause";
    $total_payments = fetchOne($count_sql, $params)['count'];
} catch (Exception $e) {
    logError("Payments count error: " . $e->getMessage());
}

$total_pages = ceil($total_payments / $per_page);

// الحصول على المدفوعات
$payments = [];
try {
    $sql = "SELECT p.*, sr.request_title, s.service_name_ar
            FROM payments p 
            LEFT JOIN service_requests sr ON p.request_id = sr.id 
            LEFT JOIN services s ON sr.service_id = s.id
            WHERE $where_clause 
            ORDER BY p.created_at DESC 
            LIMIT :limit OFFSET :offset";
    
    $stmt = executeQuery($sql, array_merge($params, ['limit' => $per_page, 'offset' => $offset]));
    $payments = $stmt->fetchAll();
} catch (Exception $e) {
    logError("Payments fetch error: " . $e->getMessage());
}

// إحصائيات المدفوعات
$payment_stats = [
    'total_paid' => 0,
    'total_pending' => 0,
    'total_failed' => 0,
    'payment_count' => 0
];

try {
    $stats_sql = "SELECT 
                    SUM(CASE WHEN payment_status = 'completed' THEN amount ELSE 0 END) as total_paid,
                    SUM(CASE WHEN payment_status = 'pending' THEN amount ELSE 0 END) as total_pending,
                    SUM(CASE WHEN payment_status = 'failed' THEN amount ELSE 0 END) as total_failed,
                    COUNT(*) as payment_count
                  FROM payments WHERE user_id = :user_id";
    
    $payment_stats = fetchOne($stats_sql, ['user_id' => $user['id']]);
} catch (Exception $e) {
    logError("Payment stats error: " . $e->getMessage());
}

// الحصول على رسالة الفلاش
$flash_message = getFlashMessage();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجل المدفوعات - <?php echo SITE_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'muted-green': '#CAD2C5',
                        'warm-beige': '#FFE5B4',
                        'earth-green': '#52796F',
                        'sand-brown': '#8D7B68',
                        'light-gray': '#F0F0F0',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-cairo bg-light-gray">
    
    <!-- Navigation -->
    <?php include '../../frontend/components/navbar.php'; ?>
    
    <div class="container mx-auto px-4 py-8">
        
        <!-- Flash Messages -->
        <?php if ($flash_message): ?>
            <div class="mb-6 p-4 rounded-lg <?php echo $flash_message['type'] === 'success' ? 'bg-green-100 text-green-700 border border-green-300' : 'bg-red-100 text-red-700 border border-red-300'; ?>">
                <div class="flex items-center">
                    <i class="fas <?php echo $flash_message['type'] === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> ml-2"></i>
                    <?php echo htmlspecialchars($flash_message['message']); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Header -->
        <div class="mb-8">
            <nav class="flex items-center space-x-2 space-x-reverse text-sm text-gray-600 mb-4">
                <a href="<?php echo getUrl(); ?>" class="hover:text-earth-green transition-colors">الرئيسية</a>
                <i class="fas fa-chevron-left text-xs"></i>
                <a href="<?php echo getUrl('dashboard/client'); ?>" class="hover:text-earth-green transition-colors">لوحة التحكم</a>
                <i class="fas fa-chevron-left text-xs"></i>
                <span class="text-earth-green">سجل المدفوعات</span>
            </nav>
            
            <h1 class="text-3xl font-bold text-earth-green mb-2">
                سجل المدفوعات
            </h1>
            <p class="text-gray-600">
                عرض وإدارة جميع مدفوعاتك
            </p>
        </div>
        
        <!-- Payment Statistics -->
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            
            <!-- Total Paid -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">إجمالي المدفوع</p>
                        <p class="text-2xl font-bold text-green-600"><?php echo number_format($payment_stats['total_paid'] ?? 0, 2); ?></p>
                        <p class="text-xs text-gray-500">دينار جزائري</p>
                    </div>
                    <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-white text-xl"></i>
                    </div>
                </div>
            </div>
            
            <!-- Total Pending -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">في الانتظار</p>
                        <p class="text-2xl font-bold text-yellow-600"><?php echo number_format($payment_stats['total_pending'] ?? 0, 2); ?></p>
                        <p class="text-xs text-gray-500">دينار جزائري</p>
                    </div>
                    <div class="w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clock text-white text-xl"></i>
                    </div>
                </div>
            </div>
            
            <!-- Total Failed -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">فاشلة</p>
                        <p class="text-2xl font-bold text-red-600"><?php echo number_format($payment_stats['total_failed'] ?? 0, 2); ?></p>
                        <p class="text-xs text-gray-500">دينار جزائري</p>
                    </div>
                    <div class="w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-times-circle text-white text-xl"></i>
                    </div>
                </div>
            </div>
            
            <!-- Payment Count -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">عدد المعاملات</p>
                        <p class="text-2xl font-bold text-earth-green"><?php echo $payment_stats['payment_count'] ?? 0; ?></p>
                        <p class="text-xs text-gray-500">معاملة</p>
                    </div>
                    <div class="w-12 h-12 bg-earth-green rounded-lg flex items-center justify-center">
                        <i class="fas fa-credit-card text-white text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Search and Filter -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <form method="GET" class="grid md:grid-cols-3 gap-4">
                
                <!-- Search -->
                <div class="md:col-span-2">
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-2">
                        البحث
                    </label>
                    <div class="relative">
                        <input type="text" 
                               id="search" 
                               name="search" 
                               value="<?php echo htmlspecialchars($search); ?>"
                               class="w-full px-4 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors"
                               placeholder="ابحث في عنوان الطلب أو رقم المعاملة...">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                    </div>
                </div>
                
                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                        الحالة
                    </label>
                    <select id="status" 
                            name="status"
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                        <option value="">جميع الحالات</option>
                        <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>مكتمل</option>
                        <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>في الانتظار</option>
                        <option value="failed" <?php echo $status_filter === 'failed' ? 'selected' : ''; ?>>فاشل</option>
                    </select>
                </div>
            </form>
            
            <div class="mt-4 flex items-center justify-between">
                <button onclick="document.querySelector('form').submit()" 
                        class="bg-earth-green text-white px-6 py-2 rounded-lg font-semibold hover:bg-sand-brown transition-all duration-300">
                    <i class="fas fa-filter ml-2"></i>
                    تصفية
                </button>
                
                <?php if (!empty($search) || !empty($status_filter)): ?>
                    <a href="<?php echo getUrl('dashboard/client/payments.php'); ?>" 
                       class="text-sm text-gray-600 hover:text-earth-green transition-colors">
                        <i class="fas fa-times ml-1"></i>
                        مسح التصفية
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Payments List -->
        <div class="bg-white rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-semibold text-earth-green">
                        سجل المدفوعات (<?php echo $total_payments; ?>)
                    </h2>
                    <?php if (!empty($search) || !empty($status_filter)): ?>
                        <a href="<?php echo getUrl('dashboard/client/payments.php'); ?>"
                           class="text-sm text-gray-600 hover:text-earth-green transition-colors">
                            <i class="fas fa-times ml-1"></i>
                            مسح التصفية
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <div class="overflow-x-auto">
                <?php if (empty($payments)): ?>
                    <div class="text-center py-12">
                        <i class="fas fa-credit-card text-4xl text-gray-400 mb-4"></i>
                        <p class="text-gray-600 mb-4">
                            <?php if (!empty($search) || !empty($status_filter)): ?>
                                لا توجد مدفوعات تطابق معايير البحث
                            <?php else: ?>
                                لا توجد مدفوعات حتى الآن
                            <?php endif; ?>
                        </p>
                        <a href="<?php echo getUrl('services/request.php'); ?>"
                           class="bg-earth-green text-white px-6 py-2 rounded-lg hover:bg-sand-brown transition-colors">
                            اطلب خدمة الآن
                        </a>
                    </div>
                <?php else: ?>
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المعاملة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الطلب</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">طريقة الدفع</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التاريخ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($payments as $payment): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4">
                                        <div class="text-sm font-medium text-gray-900">
                                            <?php echo htmlspecialchars($payment['transaction_id']); ?>
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            #<?php echo $payment['id']; ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm font-medium text-gray-900">
                                            <?php echo htmlspecialchars($payment['request_title'] ?? 'طلب محذوف'); ?>
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            <?php echo htmlspecialchars($payment['service_name_ar'] ?? 'خدمة محاسبية'); ?>
                                        </div>
                                        <div class="text-xs text-gray-500">
                                            طلب #<?php echo $payment['request_id']; ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-semibold text-gray-900">
                                            <?php echo formatCurrency($payment['amount']); ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php
                                        $payment_methods = [
                                            'credit_card' => 'بطاقة ائتمان',
                                            'bank_transfer' => 'تحويل بنكي',
                                            'paypal' => 'PayPal'
                                        ];
                                        echo $payment_methods[$payment['payment_method']] ?? $payment['payment_method'];
                                        ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php
                                        $status_classes = [
                                            'completed' => 'bg-green-100 text-green-800',
                                            'pending' => 'bg-yellow-100 text-yellow-800',
                                            'failed' => 'bg-red-100 text-red-800'
                                        ];

                                        $status_text = [
                                            'completed' => 'مكتمل',
                                            'pending' => 'في الانتظار',
                                            'failed' => 'فاشل'
                                        ];
                                        ?>
                                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $status_classes[$payment['payment_status']] ?? 'bg-gray-100 text-gray-800'; ?>">
                                            <?php echo $status_text[$payment['payment_status']] ?? $payment['payment_status']; ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <div><?php echo date('Y-m-d', strtotime($payment['created_at'])); ?></div>
                                        <div class="text-xs"><?php echo date('H:i', strtotime($payment['created_at'])); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <?php if ($payment['request_id']): ?>
                                                <a href="<?php echo getUrl('dashboard/client/request-details.php?id=' . $payment['request_id']); ?>"
                                                   class="text-earth-green hover:text-sand-brown transition-colors"
                                                   title="عرض الطلب">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            <?php endif; ?>

                                            <?php if ($payment['payment_status'] === 'completed'): ?>
                                                <button onclick="printReceipt(<?php echo $payment['id']; ?>)"
                                                        class="text-blue-600 hover:text-blue-800 transition-colors"
                                                        title="طباعة الإيصال">
                                                    <i class="fas fa-print"></i>
                                                </button>
                                            <?php endif; ?>

                                            <?php if ($payment['payment_status'] === 'failed'): ?>
                                                <a href="<?php echo getUrl('payment/checkout.php?request_id=' . $payment['request_id']); ?>"
                                                   class="text-orange-600 hover:text-orange-800 transition-colors"
                                                   title="إعادة المحاولة">
                                                    <i class="fas fa-redo"></i>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include '../../frontend/components/footer.php'; ?>

    <!-- Scripts -->
    <script src="../../assets/js/main.js"></script>

    <script>
        // Print receipt function
        function printReceipt(paymentId) {
            // Open receipt in new window for printing
            const receiptWindow = window.open(`<?php echo getUrl('payment/receipt.php?id='); ?>${paymentId}`, '_blank', 'width=800,height=600');
            receiptWindow.onload = function() {
                receiptWindow.print();
            };
        }
    </script>
</body>
</html>
