<?php
/**
 * AI Accounting Assistant
 * مساعد المحاسبة الذكي
 */

require_once 'backend/config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(getUrl('login.php'));
}

$user = getCurrentUser();

// معالجة طلب المحادثة
$response = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['message'])) {
    $user_message = sanitize($_POST['message'] ?? '');
    
    if (!empty($user_message)) {
        try {
            $response = getAIResponse($user_message, $user);
            
            // حفظ المحادثة في قاعدة البيانات (اختياري)
            try {
                global $database;
                $chat_data = [
                    'user_id' => $user['id'],
                    'user_message' => $user_message,
                    'ai_response' => $response,
                    'created_at' => date('Y-m-d H:i:s')
                ];
                
                // يمكن إنشاء جدول للمحادثات إذا لزم الأمر
                logError("AI Chat: User {$user['id']} asked: " . substr($user_message, 0, 100));
            } catch (Exception $e) {
                // تجاهل أخطاء حفظ المحادثة
            }
            
        } catch (Exception $e) {
            $error_message = 'حدث خطأ أثناء التواصل مع المساعد الذكي. يرجى المحاولة مرة أخرى.';
            logError("AI Assistant error: " . $e->getMessage());
        }
    } else {
        $error_message = 'يرجى كتابة سؤالك أو رسالتك';
    }
}

/**
 * الحصول على رد من الذكاء الاصطناعي
 */
function getAIResponse($message, $user) {
    $api_key = OPENROUTER_API_KEY;
    $api_url = OPENROUTER_API_URL;
    $model = AI_MODEL;
    
    // إعداد السياق للمحاسب الذكي
    $system_prompt = "أنت مساعد محاسبة ذكي متخصص في المحاسبة والضرائب في الجزائر.

مهامك:
1. تقديم المشورة المحاسبية والضريبية
2. شرح المفاهيم المحاسبية بطريقة مبسطة
3. مساعدة في فهم القوانين الضريبية الجزائرية
4. تقديم نصائح لتحسين الإدارة المالية
5. الإجابة على الأسئلة المتعلقة بالقوائم المالية

قواعد مهمة:
- اجعل إجاباتك باللغة العربية
- كن دقيقاً ومهنياً
- إذا لم تكن متأكداً من معلومة، اذكر ذلك
- انصح بالرجوع لمحاسب مختص للحالات المعقدة
- لا تقدم نصائح قانونية محددة، بل معلومات عامة
- استخدم الدينار الجزائري (د.ج) كعملة في الأمثلة

معلومات المستخدم:
- الاسم: {$user['full_name']}
- الدور: {$user['role']}
- الشركة: " . ($user['company_name'] ?? 'غير محدد');

    $data = [
        'model' => $model,
        'messages' => [
            [
                'role' => 'system',
                'content' => $system_prompt
            ],
            [
                'role' => 'user',
                'content' => $message
            ]
        ],
        'max_tokens' => 800,
        'temperature' => 0.7
    ];
    
    $headers = [
        'Authorization: Bearer ' . $api_key,
        'Content-Type: application/json',
        'HTTP-Referer: ' . SITE_URL,
        'X-Title: ' . SITE_NAME
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        throw new Exception("خطأ في الاتصال: " . $error);
    }
    
    if ($http_code !== 200) {
        if ($http_code === 401) {
            throw new Exception("مفتاح API غير صحيح أو منتهي الصلاحية. يرجى التحقق من إعدادات الذكاء الاصطناعي.");
        } else {
            throw new Exception("خطأ في الخدمة: " . $http_code);
        }
    }
    
    $result = json_decode($response, true);
    
    if (!$result || !isset($result['choices'][0]['message']['content'])) {
        throw new Exception("استجابة غير صحيحة من الخدمة");
    }
    
    return trim($result['choices'][0]['message']['content']);
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المساعد الذكي - <?php echo SITE_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'muted-green': '#CAD2C5',
                        'warm-beige': '#FFE5B4',
                        'earth-green': '#52796F',
                        'sand-brown': '#8D7B68',
                        'light-gray': '#F0F0F0',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-cairo bg-light-gray">
    
    <!-- Navigation -->
    <?php include 'frontend/components/navbar.php'; ?>
    
    <div class="container mx-auto px-4 py-8">
        
        <!-- Header -->
        <div class="mb-8">
            <nav class="flex items-center space-x-2 space-x-reverse text-sm text-gray-600 mb-4">
                <a href="<?php echo getUrl(); ?>" class="hover:text-earth-green transition-colors">الرئيسية</a>
                <i class="fas fa-chevron-left text-xs"></i>
                <span class="text-earth-green">المساعد الذكي</span>
            </nav>
            
            <div class="text-center">
                <div class="w-20 h-20 bg-earth-green rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-robot text-white text-3xl"></i>
                </div>
                <h1 class="text-3xl font-bold text-earth-green mb-2">
                    المساعد المحاسبي الذكي
                </h1>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    احصل على إجابات فورية لأسئلتك المحاسبية والضريبية من مساعدنا الذكي المتخصص
                </p>
            </div>
        </div>
        
        <div class="max-w-4xl mx-auto">
            
            <!-- Chat Interface -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                
                <!-- Chat Header -->
                <div class="bg-earth-green text-white p-6">
                    <div class="flex items-center space-x-3 space-x-reverse">
                        <div class="w-12 h-12 bg-warm-beige rounded-full flex items-center justify-center">
                            <i class="fas fa-robot text-earth-green text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold">مساعد المحاسبة</h3>
                            <p class="text-sm opacity-90">متاح 24/7 لمساعدتك</p>
                        </div>
                    </div>
                </div>
                
                <!-- Chat Messages -->
                <div class="p-6 min-h-96 max-h-96 overflow-y-auto" id="chatMessages">
                    
                    <!-- Welcome Message -->
                    <div class="flex items-start space-x-3 space-x-reverse mb-6">
                        <div class="w-10 h-10 bg-earth-green rounded-full flex items-center justify-center">
                            <i class="fas fa-robot text-white"></i>
                        </div>
                        <div class="bg-gray-100 rounded-lg p-4 max-w-md">
                            <p class="text-gray-800">
                                مرحباً <?php echo htmlspecialchars($user['full_name']); ?>! 👋<br><br>
                                أنا مساعدك المحاسبي الذكي. يمكنني مساعدتك في:
                            </p>
                            <ul class="mt-3 text-sm text-gray-700 space-y-1">
                                <li>• الأسئلة المحاسبية والضريبية</li>
                                <li>• شرح المفاهيم المالية</li>
                                <li>• نصائح الإدارة المالية</li>
                                <li>• القوانين الضريبية الجزائرية</li>
                            </ul>
                            <p class="mt-3 text-sm text-gray-600">
                                كيف يمكنني مساعدتك اليوم؟
                            </p>
                        </div>
                    </div>
                    
                    <!-- User Message (if exists) -->
                    <?php if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($_POST['message'])): ?>
                        <div class="flex items-start space-x-3 space-x-reverse mb-6 justify-end">
                            <div class="bg-earth-green text-white rounded-lg p-4 max-w-md">
                                <p><?php echo nl2br(htmlspecialchars($_POST['message'])); ?></p>
                            </div>
                            <div class="w-10 h-10 bg-sand-brown rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white"></i>
                            </div>
                        </div>
                        
                        <!-- AI Response -->
                        <?php if ($response): ?>
                            <div class="flex items-start space-x-3 space-x-reverse mb-6">
                                <div class="w-10 h-10 bg-earth-green rounded-full flex items-center justify-center">
                                    <i class="fas fa-robot text-white"></i>
                                </div>
                                <div class="bg-gray-100 rounded-lg p-4 max-w-md">
                                    <p class="text-gray-800 whitespace-pre-line"><?php echo htmlspecialchars($response); ?></p>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Error Message -->
                        <?php if ($error_message): ?>
                            <div class="flex items-start space-x-3 space-x-reverse mb-6">
                                <div class="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-exclamation-triangle text-white"></i>
                                </div>
                                <div class="bg-red-100 border border-red-300 rounded-lg p-4 max-w-md">
                                    <p class="text-red-700"><?php echo htmlspecialchars($error_message); ?></p>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
                
                <!-- Chat Input -->
                <div class="border-t border-gray-200 p-6">
                    <form method="POST" class="flex space-x-3 space-x-reverse">
                        <div class="flex-1">
                            <textarea name="message" 
                                      rows="3"
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors resize-none"
                                      placeholder="اكتب سؤالك هنا... مثل: كيف أحسب ضريبة القيمة المضافة؟"
                                      required></textarea>
                        </div>
                        <button type="submit" 
                                class="bg-earth-green text-white px-6 py-3 rounded-lg hover:bg-sand-brown transition-all duration-300 self-end">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </form>
                    
                    <div class="mt-4 text-center">
                        <p class="text-xs text-gray-500">
                            💡 نصيحة: كن محدداً في سؤالك للحصول على إجابة أفضل
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Quick Questions -->
            <div class="mt-8 bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold text-earth-green mb-4">
                    <i class="fas fa-lightbulb ml-2"></i>
                    أسئلة شائعة
                </h3>
                
                <div class="grid md:grid-cols-2 gap-4">
                    <button onclick="askQuestion('كيف أحسب ضريبة القيمة المضافة في السعودية؟')" 
                            class="text-right p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-calculator text-earth-green ml-2"></i>
                        كيف أحسب ضريبة القيمة المضافة؟
                    </button>
                    
                    <button onclick="askQuestion('ما هي القوائم المالية الأساسية؟')" 
                            class="text-right p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-chart-line text-earth-green ml-2"></i>
                        ما هي القوائم المالية الأساسية؟
                    </button>
                    
                    <button onclick="askQuestion('كيف أدير التدفق النقدي لشركتي؟')" 
                            class="text-right p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-money-bill-wave text-earth-green ml-2"></i>
                        كيف أدير التدفق النقدي؟
                    </button>
                    
                    <button onclick="askQuestion('ما هي المصروفات القابلة للخصم ضريبياً؟')" 
                            class="text-right p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-receipt text-earth-green ml-2"></i>
                        المصروفات القابلة للخصم ضريبياً
                    </button>
                </div>
            </div>
            
            <!-- Disclaimer -->
            <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div class="flex items-start space-x-3 space-x-reverse">
                    <i class="fas fa-exclamation-triangle text-yellow-600 mt-1"></i>
                    <div class="text-sm text-yellow-800">
                        <p class="font-semibold mb-1">تنبيه مهم:</p>
                        <p>
                            المعلومات المقدمة من المساعد الذكي هي للإرشاد العام فقط ولا تعتبر استشارة مهنية. 
                            للحالات المعقدة أو القرارات المهمة، يُنصح بالرجوع إلى محاسب مختص.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Footer -->
    <?php include 'frontend/components/footer.php'; ?>
    
    <!-- Scripts -->
    <script src="assets/js/main.js"></script>
    
    <script>
        // Auto-scroll to bottom of chat
        function scrollToBottom() {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // Ask predefined question
        function askQuestion(question) {
            document.querySelector('textarea[name="message"]').value = question;
            document.querySelector('textarea[name="message"]').focus();
        }
        
        // Auto-scroll on page load
        window.addEventListener('load', scrollToBottom);
        
        // Auto-resize textarea
        document.querySelector('textarea[name="message"]').addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });
    </script>
</body>
</html>
