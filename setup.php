<?php
/**
 * محاسبك الرقمي - معالج الإعداد
 * Digital Accountant - Setup Wizard
 * 
 * This file provides a beautiful UI for setting up the database and configuration
 */

// Start session for storing setup progress
session_start();

// Initialize setup steps
$steps = [
    1 => 'مرحباً بك',
    2 => 'متطلبات النظام',
    3 => 'إعداد قاعدة البيانات',
    4 => 'إعداد الموقع',
    5 => 'إنهاء الإعداد'
];

$current_step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$current_step = max(1, min(5, $current_step));

// Handle form submissions
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($current_step) {
        case 3: // Database setup
            $db_host = $_POST['db_host'] ?? 'localhost';
            $db_name = $_POST['db_name'] ?? 'mohassebk_db';
            $db_user = $_POST['db_user'] ?? 'root';
            $db_pass = $_POST['db_pass'] ?? '';
            
            // Test database connection
            try {
                $pdo = new PDO("mysql:host=$db_host", $db_user, $db_pass);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // Read and execute SQL file
                $sql_file = __DIR__ . '/database/mohassebk_db.sql';
                if (file_exists($sql_file)) {
                    $sql = file_get_contents($sql_file);
                    $pdo->exec($sql);
                    
                    // Store database config in session
                    $_SESSION['db_config'] = [
                        'host' => $db_host,
                        'name' => $db_name,
                        'user' => $db_user,
                        'pass' => $db_pass
                    ];
                    
                    $message = 'تم إنشاء قاعدة البيانات بنجاح!';
                    $current_step = 4;
                } else {
                    $error = 'ملف قاعدة البيانات غير موجود!';
                }
            } catch (PDOException $e) {
                $error = 'خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage();
            }
            break;
            
        case 4: // Site configuration
            $site_name = $_POST['site_name'] ?? 'المحاسبك الرقمي';
            $site_email = $_POST['site_email'] ?? '<EMAIL>';
            $site_phone = $_POST['site_phone'] ?? '+213123456789';
            $admin_email = $_POST['admin_email'] ?? '<EMAIL>';
            $admin_password = $_POST['admin_password'] ?? '';
            
            if (!empty($admin_password) && isset($_SESSION['db_config'])) {
                // Create config.php file
                $config_content = generateConfigFile($_SESSION['db_config'], [
                    'site_name' => $site_name,
                    'site_email' => $site_email,
                    'site_phone' => $site_phone
                ]);
                
                if (file_put_contents(__DIR__ . '/config.php', $config_content)) {
                    // Update admin user in database
                    try {
                        $db = $_SESSION['db_config'];
                        $pdo = new PDO("mysql:host={$db['host']};dbname={$db['name']}", $db['user'], $db['pass']);
                        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                        
                        $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
                        $stmt = $pdo->prepare("UPDATE users SET email = ?, password = ? WHERE username = 'admin'");
                        $stmt->execute([$admin_email, $hashed_password]);
                        
                        $message = 'تم إعداد الموقع بنجاح!';
                        $current_step = 5;
                    } catch (PDOException $e) {
                        $error = 'خطأ في تحديث بيانات المدير: ' . $e->getMessage();
                    }
                } else {
                    $error = 'فشل في إنشاء ملف الإعدادات!';
                }
            } else {
                $error = 'يرجى ملء جميع الحقول المطلوبة!';
            }
            break;
    }
}

function generateConfigFile($db_config, $site_config) {
    return "<?php
/**
 * ملف إعدادات محاسبك الرقمي
 * Digital Accountant Configuration File
 * Generated on: " . date('Y-m-d H:i:s') . "
 */

// Database Configuration
define('DB_HOST', '{$db_config['host']}');
define('DB_NAME', '{$db_config['name']}');
define('DB_USER', '{$db_config['user']}');
define('DB_PASS', '{$db_config['pass']}');

// Site Configuration
define('SITE_NAME', '{$site_config['site_name']}');
define('SITE_EMAIL', '{$site_config['site_email']}');
define('SITE_PHONE', '{$site_config['site_phone']}');
define('SITE_URL', 'http://' . \$_SERVER['HTTP_HOST'] . dirname(\$_SERVER['SCRIPT_NAME']));

// Security
define('SECRET_KEY', '" . bin2hex(random_bytes(32)) . "');

// Timezone
date_default_timezone_set('Africa/Algiers');

// Error Reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database Connection Function
function getDBConnection() {
    try {
        \$pdo = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
        \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        \$pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        return \$pdo;
    } catch (PDOException \$e) {
        die('Database connection failed: ' . \$e->getMessage());
    }
}
?>";
}

// Check if setup is already completed
if (file_exists(__DIR__ . '/config.php') && $current_step === 1) {
    $setup_completed = true;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد محاسبك الرقمي - Setup Wizard</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #52796F;
            --secondary-color: #CAD2C5;
            --accent-color: #FFE5B4;
            --success-color: #28a745;
            --error-color: #dc3545;
            --warning-color: #ffc107;
            --text-dark: #333333;
            --text-light: #666666;
            --bg-light: #F0F0F0;
            --white: #FFFFFF;
            --shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            direction: rtl;
            text-align: right;
        }

        .setup-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .setup-card {
            background: var(--white);
            border-radius: 20px;
            box-shadow: var(--shadow);
            overflow: hidden;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .setup-header {
            background: var(--primary-color);
            color: var(--white);
            padding: 30px;
            text-align: center;
        }

        .setup-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .setup-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .progress-bar {
            background: rgba(255, 255, 255, 0.2);
            height: 8px;
            border-radius: 4px;
            margin-top: 20px;
            overflow: hidden;
        }

        .progress-fill {
            background: var(--accent-color);
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
            width: <?= ($current_step / 5) * 100 ?>%;
        }

        .steps-nav {
            display: flex;
            justify-content: space-between;
            padding: 20px 30px;
            background: var(--bg-light);
            border-bottom: 1px solid #ddd;
        }

        .step {
            display: flex;
            align-items: center;
            font-size: 0.9rem;
            color: var(--text-light);
            transition: color 0.3s ease;
        }

        .step.active {
            color: var(--primary-color);
            font-weight: 600;
        }

        .step.completed {
            color: var(--success-color);
        }

        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #ddd;
            color: var(--white);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .step.active .step-number {
            background: var(--primary-color);
        }

        .step.completed .step-number {
            background: var(--success-color);
        }

        .setup-content {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-dark);
        }

        .form-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 1rem;
            font-family: inherit;
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: var(--primary-color);
            color: var(--white);
        }

        .btn-primary:hover {
            background: #3d5a50;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: var(--secondary-color);
            color: var(--text-dark);
        }

        .btn-secondary:hover {
            background: #b8c4b8;
        }

        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-color: var(--success-color);
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border-color: var(--error-color);
        }

        .requirements-list {
            list-style: none;
            padding: 0;
        }

        .requirements-list li {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .requirement-status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-ok {
            background: var(--success-color);
            color: var(--white);
        }

        .status-error {
            background: var(--error-color);
            color: var(--white);
        }

        .welcome-content {
            text-align: center;
            padding: 40px 0;
        }

        .welcome-icon {
            font-size: 4rem;
            color: var(--primary-color);
            margin-bottom: 20px;
        }

        .setup-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        @media (max-width: 768px) {
            .setup-container {
                padding: 10px;
            }
            
            .setup-header h1 {
                font-size: 2rem;
            }
            
            .steps-nav {
                flex-direction: column;
                gap: 10px;
            }
            
            .setup-actions {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-card">
            <!-- Header -->
            <div class="setup-header">
                <h1><i class="fas fa-calculator"></i> محاسبك الرقمي</h1>
                <p>معالج إعداد النظام - Setup Wizard</p>
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
            </div>

            <!-- Steps Navigation -->
            <div class="steps-nav">
                <?php foreach ($steps as $step_num => $step_name): ?>
                    <div class="step <?= $step_num === $current_step ? 'active' : ($step_num < $current_step ? 'completed' : '') ?>">
                        <div class="step-number">
                            <?= $step_num < $current_step ? '<i class="fas fa-check"></i>' : $step_num ?>
                        </div>
                        <span><?= $step_name ?></span>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Content -->
            <div class="setup-content">
                <?php if ($message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($setup_completed) && $setup_completed): ?>
                    <!-- Setup Already Completed -->
                    <div class="welcome-content">
                        <div class="welcome-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h2>تم إعداد النظام مسبقاً!</h2>
                        <p>يبدو أن النظام تم إعداده مسبقاً. يمكنك الآن الوصول إلى الموقع.</p>
                        <div style="margin-top: 30px;">
                            <a href="index.php" class="btn btn-primary">
                                <i class="fas fa-home"></i> الذهاب إلى الموقع
                            </a>
                            <a href="?step=1&force=1" class="btn btn-secondary">
                                <i class="fas fa-redo"></i> إعادة الإعداد
                            </a>
                        </div>
                    </div>

                <?php elseif ($current_step === 1): ?>
                    <!-- Welcome Step -->
                    <div class="welcome-content">
                        <div class="welcome-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <h2>مرحباً بك في محاسبك الرقمي!</h2>
                        <p>سيساعدك هذا المعالج على إعداد النظام خطوة بخطوة. العملية بسيطة وستستغرق بضع دقائق فقط.</p>

                        <div style="margin: 30px 0;">
                            <h3>ما ستحتاجه:</h3>
                            <ul style="text-align: right; margin: 20px 0;">
                                <li>✅ خادم ويب يدعم PHP 7.4 أو أحدث</li>
                                <li>✅ قاعدة بيانات MySQL 5.7 أو أحدث</li>
                                <li>✅ بيانات الاتصال بقاعدة البيانات</li>
                                <li>✅ عنوان بريد إلكتروني للمدير</li>
                            </ul>
                        </div>
                    </div>

                <?php elseif ($current_step === 2): ?>
                    <!-- System Requirements -->
                    <h2><i class="fas fa-cogs"></i> فحص متطلبات النظام</h2>
                    <p>يتم فحص متطلبات النظام للتأكد من توافق الخادم...</p>

                    <ul class="requirements-list">
                        <li>
                            <span>إصدار PHP (7.4 أو أحدث)</span>
                            <span class="requirement-status <?= version_compare(PHP_VERSION, '7.4.0', '>=') ? 'status-ok' : 'status-error' ?>">
                                <?= PHP_VERSION ?>
                            </span>
                        </li>
                        <li>
                            <span>امتداد PDO MySQL</span>
                            <span class="requirement-status <?= extension_loaded('pdo_mysql') ? 'status-ok' : 'status-error' ?>">
                                <?= extension_loaded('pdo_mysql') ? 'متوفر' : 'غير متوفر' ?>
                            </span>
                        </li>
                        <li>
                            <span>امتداد JSON</span>
                            <span class="requirement-status <?= extension_loaded('json') ? 'status-ok' : 'status-error' ?>">
                                <?= extension_loaded('json') ? 'متوفر' : 'غير متوفر' ?>
                            </span>
                        </li>
                        <li>
                            <span>امتداد cURL</span>
                            <span class="requirement-status <?= extension_loaded('curl') ? 'status-ok' : 'status-error' ?>">
                                <?= extension_loaded('curl') ? 'متوفر' : 'غير متوفر' ?>
                            </span>
                        </li>
                        <li>
                            <span>صلاحيات الكتابة</span>
                            <span class="requirement-status <?= is_writable(__DIR__) ? 'status-ok' : 'status-error' ?>">
                                <?= is_writable(__DIR__) ? 'متوفرة' : 'غير متوفرة' ?>
                            </span>
                        </li>
                    </ul>

                <?php elseif ($current_step === 3): ?>
                    <!-- Database Setup -->
                    <h2><i class="fas fa-database"></i> إعداد قاعدة البيانات</h2>
                    <p>يرجى إدخال بيانات الاتصال بقاعدة البيانات:</p>

                    <form method="POST">
                        <div class="form-group">
                            <label class="form-label">عنوان الخادم:</label>
                            <input type="text" name="db_host" class="form-input" value="localhost" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">اسم قاعدة البيانات:</label>
                            <input type="text" name="db_name" class="form-input" value="mohassebk_db" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">اسم المستخدم:</label>
                            <input type="text" name="db_user" class="form-input" value="root" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">كلمة المرور:</label>
                            <input type="password" name="db_pass" class="form-input" placeholder="اتركه فارغاً إذا لم تكن هناك كلمة مرور">
                        </div>

                        <div class="setup-actions">
                            <a href="?step=2" class="btn btn-secondary">
                                <i class="fas fa-arrow-right"></i> السابق
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-database"></i> إنشاء قاعدة البيانات
                            </button>
                        </div>
                    </form>

                <?php elseif ($current_step === 4): ?>
                    <!-- Site Configuration -->
                    <h2><i class="fas fa-cog"></i> إعداد الموقع</h2>
                    <p>يرجى إدخال المعلومات الأساسية للموقع:</p>

                    <form method="POST">
                        <div class="form-group">
                            <label class="form-label">اسم الموقع:</label>
                            <input type="text" name="site_name" class="form-input" value="المحاسبك الرقمي" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">البريد الإلكتروني للموقع:</label>
                            <input type="email" name="site_email" class="form-input" value="<EMAIL>" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">رقم الهاتف:</label>
                            <input type="text" name="site_phone" class="form-input" value="+213123456789" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">البريد الإلكتروني للمدير:</label>
                            <input type="email" name="admin_email" class="form-input" value="<EMAIL>" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">كلمة مرور المدير:</label>
                            <input type="password" name="admin_password" class="form-input" placeholder="أدخل كلمة مرور قوية" required>
                        </div>

                        <div class="setup-actions">
                            <a href="?step=3" class="btn btn-secondary">
                                <i class="fas fa-arrow-right"></i> السابق
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ الإعدادات
                            </button>
                        </div>
                    </form>

                <?php elseif ($current_step === 5): ?>
                    <!-- Setup Complete -->
                    <div class="welcome-content">
                        <div class="welcome-icon">
                            <i class="fas fa-check-circle" style="color: var(--success-color);"></i>
                        </div>
                        <h2>تم إنهاء الإعداد بنجاح! 🎉</h2>
                        <p>تم إعداد نظام محاسبك الرقمي بنجاح. يمكنك الآن البدء في استخدام النظام.</p>

                        <div style="margin: 30px 0; text-align: right;">
                            <h3>معلومات مهمة:</h3>
                            <ul style="margin: 20px 0;">
                                <li>✅ تم إنشاء قاعدة البيانات وجداولها</li>
                                <li>✅ تم إنشاء ملف الإعدادات (config.php)</li>
                                <li>✅ تم إنشاء حساب المدير</li>
                                <li>✅ تم إدراج البيانات الأساسية</li>
                            </ul>

                            <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
                                <h4>بيانات تسجيل الدخول:</h4>
                                <p><strong>البريد الإلكتروني:</strong> <?= htmlspecialchars($_SESSION['admin_email'] ?? '<EMAIL>') ?></p>
                                <p><strong>كلمة المرور:</strong> التي قمت بإدخالها في الخطوة السابقة</p>
                            </div>
                        </div>

                        <div style="margin-top: 30px;">
                            <a href="index.php" class="btn btn-primary">
                                <i class="fas fa-home"></i> الذهاب إلى الموقع
                            </a>
                            <a href="admin/" class="btn btn-secondary">
                                <i class="fas fa-user-shield"></i> لوحة الإدارة
                            </a>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Navigation for steps 1 and 2 -->
                <?php if ($current_step === 1): ?>
                    <div class="setup-actions">
                        <div></div>
                        <a href="?step=2" class="btn btn-primary">
                            <i class="fas fa-arrow-left"></i> التالي
                        </a>
                    </div>
                <?php elseif ($current_step === 2): ?>
                    <div class="setup-actions">
                        <a href="?step=1" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i> السابق
                        </a>
                        <?php
                        $can_continue = version_compare(PHP_VERSION, '7.4.0', '>=') &&
                                       extension_loaded('pdo_mysql') &&
                                       extension_loaded('json') &&
                                       is_writable(__DIR__);
                        ?>
                        <a href="?step=3" class="btn btn-primary <?= $can_continue ? '' : 'disabled' ?>">
                            <i class="fas fa-arrow-left"></i> التالي
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Animate progress bar
            const progressFill = document.querySelector('.progress-fill');
            if (progressFill) {
                setTimeout(() => {
                    progressFill.style.width = '<?= ($current_step / 5) * 100 ?>%';
                }, 300);
            }

            // Form validation
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    const requiredFields = form.querySelectorAll('[required]');
                    let isValid = true;

                    requiredFields.forEach(field => {
                        if (!field.value.trim()) {
                            field.style.borderColor = 'var(--error-color)';
                            isValid = false;
                        } else {
                            field.style.borderColor = '#ddd';
                        }
                    });

                    if (!isValid) {
                        e.preventDefault();
                        alert('يرجى ملء جميع الحقول المطلوبة');
                    }
                });
            });
        });
    </script>
</body>
</html>
