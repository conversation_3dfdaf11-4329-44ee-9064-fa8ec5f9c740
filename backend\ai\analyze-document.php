<?php
/**
 * AI Document Analysis
 * تحليل المستندات بالذكاء الاصطناعي
 */

require_once '../config/config.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح لك بالوصول']);
    exit;
}

$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'accountant'])) {
    http_response_code(403);
    echo json_encode(['error' => 'غير مصرح لك بتحليل المستندات']);
    exit;
}

$document_id = (int)($_POST['document_id'] ?? 0);

if (!$document_id) {
    http_response_code(400);
    echo json_encode(['error' => 'معرف المستند مطلوب']);
    exit;
}

try {
    // الحصول على معلومات المستند
    $sql = "SELECT d.*, sr.request_title, sr.description, s.service_name_ar 
            FROM documents d 
            LEFT JOIN service_requests sr ON d.request_id = sr.id 
            LEFT JOIN services s ON sr.service_id = s.id 
            WHERE d.id = :id";
    
    $document = fetchOne($sql, ['id' => $document_id]);
    
    if (!$document) {
        http_response_code(404);
        echo json_encode(['error' => 'المستند غير موجود']);
        exit;
    }
    
    // التحقق من وجود الملف
    if (!file_exists($document['file_path'])) {
        http_response_code(404);
        echo json_encode(['error' => 'الملف غير موجود على الخادم']);
        exit;
    }
    
    // التحقق من أن الملف لم يتم تحليله مسبقاً
    if (!empty($document['ai_analysis'])) {
        echo json_encode([
            'success' => true,
            'analysis' => $document['ai_analysis'],
            'message' => 'تم استرجاع التحليل المحفوظ مسبقاً'
        ]);
        exit;
    }
    
    // إعداد البيانات للذكاء الاصطناعي
    $context = [
        'document_name' => $document['original_filename'],
        'file_type' => $document['file_type'],
        'request_title' => $document['request_title'] ?? '',
        'request_description' => $document['description'] ?? '',
        'service_type' => $document['service_name_ar'] ?? ''
    ];
    
    // إنشاء prompt للذكاء الاصطناعي
    $prompt = createAnalysisPrompt($context);
    
    // استدعاء OpenRouter API
    $analysis = callOpenRouterAPI($prompt);
    
    if ($analysis) {
        // حفظ التحليل في قاعدة البيانات
        global $database;
        $database->update('documents', 
            ['ai_analysis' => $analysis, 'is_processed' => 1], 
            'id = :id', 
            ['id' => $document_id]
        );
        
        // تسجيل العملية
        logError("AI Analysis: Document {$document_id} analyzed by user {$user['id']}");
        
        echo json_encode([
            'success' => true,
            'analysis' => $analysis,
            'message' => 'تم تحليل المستند بنجاح'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'error' => 'فشل في تحليل المستند'
        ]);
    }
    
} catch (Exception $e) {
    logError("AI Analysis error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'حدث خطأ أثناء تحليل المستند']);
}

/**
 * إنشاء prompt للذكاء الاصطناعي
 */
function createAnalysisPrompt($context) {
    $prompt = "أنت محاسب خبير متخصص في تحليل المستندات المحاسبية. قم بتحليل المستند التالي وقدم ملخصاً مفيداً:\n\n";
    
    $prompt .= "معلومات المستند:\n";
    $prompt .= "- اسم الملف: {$context['document_name']}\n";
    $prompt .= "- نوع الملف: {$context['file_type']}\n";
    
    if (!empty($context['service_type'])) {
        $prompt .= "- نوع الخدمة: {$context['service_type']}\n";
    }
    
    if (!empty($context['request_title'])) {
        $prompt .= "- عنوان الطلب: {$context['request_title']}\n";
    }
    
    if (!empty($context['request_description'])) {
        $prompt .= "- وصف الطلب: {$context['request_description']}\n";
    }
    
    $prompt .= "\nيرجى تقديم تحليل مختصر ومفيد يتضمن:\n";
    $prompt .= "1. نوع المستند المحتمل\n";
    $prompt .= "2. الغرض من المستند\n";
    $prompt .= "3. أي ملاحظات مهمة للمحاسب\n";
    $prompt .= "4. اقتراحات للخطوات التالية\n\n";
    $prompt .= "اجعل الرد باللغة العربية ومختصراً (لا يزيد عن 200 كلمة).";
    
    return $prompt;
}

/**
 * استدعاء OpenRouter API
 */
function callOpenRouterAPI($prompt) {
    $api_key = OPENROUTER_API_KEY;
    $api_url = OPENROUTER_API_URL;
    $model = AI_MODEL;
    
    $data = [
        'model' => $model,
        'messages' => [
            [
                'role' => 'user',
                'content' => $prompt
            ]
        ],
        'max_tokens' => 500,
        'temperature' => 0.7
    ];
    
    $headers = [
        'Authorization: Bearer ' . $api_key,
        'Content-Type: application/json',
        'HTTP-Referer: ' . SITE_URL,
        'X-Title: ' . SITE_NAME
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        logError("OpenRouter API cURL error: " . $error);
        return false;
    }
    
    if ($http_code !== 200) {
        logError("OpenRouter API HTTP error: " . $http_code . " - " . $response);
        return false;
    }
    
    $result = json_decode($response, true);
    
    if (!$result || !isset($result['choices'][0]['message']['content'])) {
        logError("OpenRouter API invalid response: " . $response);
        return false;
    }
    
    return trim($result['choices'][0]['message']['content']);
}
?>
