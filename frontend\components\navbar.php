<?php
/**
 * Navigation Bar Component
 * مكون شريط التنقل
 */

// التحقق من المستخدم الحالي
$current_user = getCurrentUser();
$is_logged_in = isLoggedIn();
?>

<nav class="bg-white shadow-lg sticky top-0 z-50">
    <div class="container mx-auto px-4">
        <div class="flex justify-between items-center py-4">
            
            <!-- الشعار -->
            <div class="flex items-center">
                <a href="<?php echo getUrl(); ?>" class="flex items-center space-x-3 space-x-reverse">
                    <div class="w-10 h-10 bg-earth-green rounded-lg flex items-center justify-center">
                        <i class="fas fa-calculator text-white text-xl"></i>
                    </div>
                    <span class="text-2xl font-bold text-earth-green"><?php echo SITE_NAME; ?></span>
                </a>
            </div>
            
            <!-- قائمة التنقل للشاشات الكبيرة -->
            <div class="hidden lg:flex items-center space-x-8 space-x-reverse">
                <?php if ($is_logged_in): ?>
                    <!-- قائمة المستخدم المسجل -->
                    <?php if ($current_user['role'] === 'admin'): ?>
                        <!-- قائمة المدير -->
                        <a href="<?php echo getUrl('dashboard/admin'); ?>"
                           class="text-gray-700 hover:text-earth-green transition-colors font-medium">
                            <i class="fas fa-tachometer-alt ml-1"></i>
                            لوحة الإدارة
                        </a>
                        <a href="<?php echo getUrl('dashboard/admin/users.php'); ?>"
                           class="text-gray-700 hover:text-earth-green transition-colors font-medium">
                            <i class="fas fa-users ml-1"></i>
                            إدارة المستخدمين
                        </a>
                        <a href="<?php echo getUrl('dashboard/admin/services.php'); ?>"
                           class="text-gray-700 hover:text-earth-green transition-colors font-medium">
                            <i class="fas fa-cogs ml-1"></i>
                            إدارة الخدمات
                        </a>
                        <a href="<?php echo getUrl('dashboard/admin/reports.php'); ?>"
                           class="text-gray-700 hover:text-earth-green transition-colors font-medium">
                            <i class="fas fa-chart-bar ml-1"></i>
                            التقارير
                        </a>
                    <?php elseif ($current_user['role'] === 'accountant'): ?>
                        <!-- قائمة المحاسب -->
                        <a href="<?php echo getUrl('dashboard/accountant'); ?>"
                           class="text-gray-700 hover:text-earth-green transition-colors font-medium">
                            <i class="fas fa-calculator ml-1"></i>
                            لوحة المحاسب
                        </a>
                        <a href="<?php echo getUrl('dashboard/accountant/requests.php'); ?>"
                           class="text-gray-700 hover:text-earth-green transition-colors font-medium">
                            <i class="fas fa-tasks ml-1"></i>
                            طلبات العملاء
                        </a>
                        <a href="<?php echo getUrl('dashboard/accountant/documents.php'); ?>"
                           class="text-gray-700 hover:text-earth-green transition-colors font-medium">
                            <i class="fas fa-file-alt ml-1"></i>
                            المستندات
                        </a>
                        <a href="<?php echo getUrl('dashboard/accountant/clients.php'); ?>"
                           class="text-gray-700 hover:text-earth-green transition-colors font-medium">
                            <i class="fas fa-user-tie ml-1"></i>
                            العملاء
                        </a>
                    <?php else: ?>
                        <!-- قائمة العميل -->
                        <a href="<?php echo getUrl('dashboard/client'); ?>"
                           class="text-gray-700 hover:text-earth-green transition-colors font-medium">
                            <i class="fas fa-user-circle ml-1"></i>
                            حسابي
                        </a>
                        <a href="<?php echo getUrl('dashboard/client/requests.php'); ?>"
                           class="text-gray-700 hover:text-earth-green transition-colors font-medium">
                            <i class="fas fa-clipboard-list ml-1"></i>
                            طلباتي
                        </a>
                        <a href="<?php echo getUrl('dashboard/client/documents.php'); ?>"
                           class="text-gray-700 hover:text-earth-green transition-colors font-medium">
                            <i class="fas fa-folder ml-1"></i>
                            مستنداتي
                        </a>
                        <a href="<?php echo getUrl('dashboard/client/payments.php'); ?>"
                           class="text-gray-700 hover:text-earth-green transition-colors font-medium">
                            <i class="fas fa-credit-card ml-1"></i>
                            المدفوعات
                        </a>
                    <?php endif; ?>
                <?php else: ?>
                    <!-- قائمة الزائر -->
                    <a href="<?php echo getUrl(); ?>"
                       class="text-gray-700 hover:text-earth-green transition-colors font-medium">
                        الرئيسية
                    </a>
                    <a href="<?php echo getUrl(); ?>#services"
                       class="text-gray-700 hover:text-earth-green transition-colors font-medium">
                        الخدمات
                    </a>
                    <a href="<?php echo getUrl(); ?>#features"
                       class="text-gray-700 hover:text-earth-green transition-colors font-medium">
                        المميزات
                    </a>
                    <a href="<?php echo getUrl('about.php'); ?>"
                       class="text-gray-700 hover:text-earth-green transition-colors font-medium">
                        من نحن
                    </a>
                    <a href="<?php echo getUrl('contact.php'); ?>"
                       class="text-gray-700 hover:text-earth-green transition-colors font-medium">
                        اتصل بنا
                    </a>
                <?php endif; ?>

                <!-- أيقونة الذكاء الاصطناعي - دائماً مرئية -->
                <a href="<?php echo getUrl('ai-assistant.php'); ?>"
                   class="relative group bg-gradient-to-r from-earth-green to-sand-brown text-white p-2 rounded-full hover:from-sand-brown hover:to-earth-green transition-all duration-300 shadow-lg"
                   title="المساعد الذكي">
                    <i class="fas fa-robot text-lg"></i>
                    <span class="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        المساعد الذكي
                    </span>
                </a>
            </div>
            
            <!-- أزرار تسجيل الدخول/الحساب -->
            <div class="hidden lg:flex items-center space-x-4 space-x-reverse">
                <?php if ($is_logged_in): ?>
                    <!-- قائمة المستخدم المسجل -->
                    <div class="relative group">
                        <button class="flex items-center space-x-2 space-x-reverse text-gray-700 hover:text-earth-green transition-colors">
                            <div class="w-8 h-8 bg-earth-green rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                            <span class="font-medium"><?php echo htmlspecialchars($current_user['full_name']); ?></span>
                            <i class="fas fa-chevron-down text-sm"></i>
                        </button>
                        
                        <!-- القائمة المنسدلة -->
                        <div class="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300">
                            <div class="py-2">
                                <?php if ($current_user['role'] === 'admin'): ?>
                                    <a href="<?php echo getUrl('dashboard/admin/settings.php'); ?>"
                                       class="block px-4 py-2 text-gray-700 hover:bg-muted-green hover:bg-opacity-20 transition-colors">
                                        <i class="fas fa-cog ml-2"></i>
                                        إعدادات النظام
                                    </a>
                                <?php elseif ($current_user['role'] === 'accountant'): ?>
                                    <a href="<?php echo getUrl('profile-accountant.php'); ?>"
                                       class="block px-4 py-2 text-gray-700 hover:bg-muted-green hover:bg-opacity-20 transition-colors">
                                        <i class="fas fa-user-edit ml-2"></i>
                                        الملف الشخصي
                                    </a>
                                <?php else: ?>
                                    <a href="<?php echo getUrl('profile-client.php'); ?>"
                                       class="block px-4 py-2 text-gray-700 hover:bg-muted-green hover:bg-opacity-20 transition-colors">
                                        <i class="fas fa-user-edit ml-2"></i>
                                        الملف الشخصي
                                    </a>
                                <?php endif; ?>

                                <a href="<?php echo getUrl('ai-assistant.php'); ?>"
                                   class="block px-4 py-2 text-gray-700 hover:bg-muted-green hover:bg-opacity-20 transition-colors">
                                    <i class="fas fa-robot ml-2"></i>
                                    المساعد الذكي
                                </a>

                                <div class="border-t border-gray-200 my-1"></div>

                                <a href="<?php echo getUrl('backend/auth/logout.php'); ?>"
                                   class="block px-4 py-2 text-red-600 hover:bg-red-50 transition-colors">
                                    <i class="fas fa-sign-out-alt ml-2"></i>
                                    تسجيل الخروج
                                </a>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- أزرار تسجيل الدخول والتسجيل -->
                    <a href="<?php echo getUrl('login.php'); ?>" 
                       class="text-earth-green hover:text-sand-brown transition-colors font-medium">
                        تسجيل الدخول
                    </a>
                    <a href="<?php echo getUrl('register.php'); ?>" 
                       class="bg-earth-green text-white px-6 py-2 rounded-lg hover:bg-sand-brown transition-all duration-300 font-medium">
                        إنشاء حساب
                    </a>
                <?php endif; ?>
            </div>
            
            <!-- زر القائمة للشاشات الصغيرة -->
            <div class="lg:hidden">
                <button id="mobile-menu-button" 
                        class="text-gray-700 hover:text-earth-green transition-colors">
                    <i class="fas fa-bars text-2xl"></i>
                </button>
            </div>
        </div>
        
        <!-- القائمة المحمولة -->
        <div id="mobile-menu" class="lg:hidden hidden pb-4">
            <div class="flex flex-col space-y-4">
                <?php if ($is_logged_in): ?>
                    <!-- قائمة المستخدم المسجل للموبايل -->
                    <?php if ($current_user['role'] === 'admin'): ?>
                        <a href="<?php echo getUrl('dashboard/admin'); ?>"
                           class="text-gray-700 hover:text-earth-green transition-colors font-medium py-2">
                            <i class="fas fa-tachometer-alt ml-2"></i>
                            لوحة الإدارة
                        </a>
                        <a href="<?php echo getUrl('dashboard/admin/users.php'); ?>"
                           class="text-gray-700 hover:text-earth-green transition-colors font-medium py-2">
                            <i class="fas fa-users ml-2"></i>
                            إدارة المستخدمين
                        </a>
                        <a href="<?php echo getUrl('dashboard/admin/services.php'); ?>"
                           class="text-gray-700 hover:text-earth-green transition-colors font-medium py-2">
                            <i class="fas fa-cogs ml-2"></i>
                            إدارة الخدمات
                        </a>
                        <a href="<?php echo getUrl('dashboard/admin/reports.php'); ?>"
                           class="text-gray-700 hover:text-earth-green transition-colors font-medium py-2">
                            <i class="fas fa-chart-bar ml-2"></i>
                            التقارير
                        </a>
                    <?php elseif ($current_user['role'] === 'accountant'): ?>
                        <a href="<?php echo getUrl('dashboard/accountant'); ?>"
                           class="text-gray-700 hover:text-earth-green transition-colors font-medium py-2">
                            <i class="fas fa-calculator ml-2"></i>
                            لوحة المحاسب
                        </a>
                        <a href="<?php echo getUrl('dashboard/accountant/requests.php'); ?>"
                           class="text-gray-700 hover:text-earth-green transition-colors font-medium py-2">
                            <i class="fas fa-tasks ml-2"></i>
                            طلبات العملاء
                        </a>
                        <a href="<?php echo getUrl('dashboard/accountant/documents.php'); ?>"
                           class="text-gray-700 hover:text-earth-green transition-colors font-medium py-2">
                            <i class="fas fa-file-alt ml-2"></i>
                            المستندات
                        </a>
                        <a href="<?php echo getUrl('dashboard/accountant/clients.php'); ?>"
                           class="text-gray-700 hover:text-earth-green transition-colors font-medium py-2">
                            <i class="fas fa-user-tie ml-2"></i>
                            العملاء
                        </a>
                    <?php else: ?>
                        <a href="<?php echo getUrl('dashboard/client'); ?>"
                           class="text-gray-700 hover:text-earth-green transition-colors font-medium py-2">
                            <i class="fas fa-user-circle ml-2"></i>
                            حسابي
                        </a>
                        <a href="<?php echo getUrl('dashboard/client/requests.php'); ?>"
                           class="text-gray-700 hover:text-earth-green transition-colors font-medium py-2">
                            <i class="fas fa-clipboard-list ml-2"></i>
                            طلباتي
                        </a>
                        <a href="<?php echo getUrl('dashboard/client/documents.php'); ?>"
                           class="text-gray-700 hover:text-earth-green transition-colors font-medium py-2">
                            <i class="fas fa-folder ml-2"></i>
                            مستنداتي
                        </a>
                        <a href="<?php echo getUrl('dashboard/client/payments.php'); ?>"
                           class="text-gray-700 hover:text-earth-green transition-colors font-medium py-2">
                            <i class="fas fa-credit-card ml-2"></i>
                            المدفوعات
                        </a>
                    <?php endif; ?>

                    <!-- أيقونة الذكاء الاصطناعي للموبايل -->
                    <a href="<?php echo getUrl('ai-assistant.php'); ?>"
                       class="bg-gradient-to-r from-earth-green to-sand-brown text-white px-4 py-2 rounded-lg hover:from-sand-brown hover:to-earth-green transition-all duration-300 text-center">
                        <i class="fas fa-robot ml-2"></i>
                        المساعد الذكي
                    </a>
                <?php else: ?>
                    <!-- قائمة الزائر للموبايل -->
                    <a href="<?php echo getUrl(); ?>"
                       class="text-gray-700 hover:text-earth-green transition-colors font-medium py-2">
                        الرئيسية
                    </a>
                    <a href="<?php echo getUrl(); ?>#services"
                       class="text-gray-700 hover:text-earth-green transition-colors font-medium py-2">
                        الخدمات
                    </a>
                    <a href="<?php echo getUrl(); ?>#features"
                       class="text-gray-700 hover:text-earth-green transition-colors font-medium py-2">
                        المميزات
                    </a>
                    <a href="<?php echo getUrl('about.php'); ?>"
                       class="text-gray-700 hover:text-earth-green transition-colors font-medium py-2">
                        من نحن
                    </a>
                    <a href="<?php echo getUrl('contact.php'); ?>"
                       class="text-gray-700 hover:text-earth-green transition-colors font-medium py-2">
                        اتصل بنا
                    </a>

                    <!-- أيقونة الذكاء الاصطناعي للزوار -->
                    <a href="<?php echo getUrl('ai-assistant.php'); ?>"
                       class="bg-gradient-to-r from-earth-green to-sand-brown text-white px-4 py-2 rounded-lg hover:from-sand-brown hover:to-earth-green transition-all duration-300 text-center">
                        <i class="fas fa-robot ml-2"></i>
                        المساعد الذكي
                    </a>
                <?php endif; ?>
                
                <div class="border-t border-gray-200 pt-4">
                    <?php if ($is_logged_in): ?>
                        <div class="flex items-center space-x-3 space-x-reverse mb-4">
                            <div class="w-10 h-10 bg-earth-green rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <span class="font-medium text-gray-700"><?php echo htmlspecialchars($current_user['full_name']); ?></span>
                        </div>
                        
                        <?php if ($current_user['role'] === 'admin'): ?>
                            <a href="<?php echo getUrl('dashboard/admin/settings.php'); ?>"
                               class="block text-earth-green hover:text-sand-brown transition-colors font-medium py-2">
                                <i class="fas fa-cog ml-2"></i>
                                إعدادات النظام
                            </a>
                        <?php elseif ($current_user['role'] === 'accountant'): ?>
                            <a href="<?php echo getUrl('profile-accountant.php'); ?>"
                               class="block text-earth-green hover:text-sand-brown transition-colors font-medium py-2">
                                <i class="fas fa-user-edit ml-2"></i>
                                الملف الشخصي
                            </a>
                        <?php else: ?>
                            <a href="<?php echo getUrl('profile-client.php'); ?>"
                               class="block text-earth-green hover:text-sand-brown transition-colors font-medium py-2">
                                <i class="fas fa-user-edit ml-2"></i>
                                الملف الشخصي
                            </a>
                        <?php endif; ?>
                        
                        <a href="<?php echo getUrl('backend/auth/logout.php'); ?>" 
                           class="block text-red-600 hover:text-red-700 transition-colors font-medium py-2">
                            <i class="fas fa-sign-out-alt ml-2"></i>
                            تسجيل الخروج
                        </a>
                    <?php else: ?>
                        <div class="flex flex-col space-y-3">
                            <a href="<?php echo getUrl('login.php'); ?>" 
                               class="text-earth-green hover:text-sand-brown transition-colors font-medium text-center py-2">
                                تسجيل الدخول
                            </a>
                            <a href="<?php echo getUrl('register.php'); ?>" 
                               class="bg-earth-green text-white px-6 py-3 rounded-lg hover:bg-sand-brown transition-all duration-300 font-medium text-center">
                                إنشاء حساب
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</nav>

<script>
// التحكم في القائمة المحمولة
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
            
            // تغيير أيقونة الزر
            const icon = mobileMenuButton.querySelector('i');
            if (mobileMenu.classList.contains('hidden')) {
                icon.className = 'fas fa-bars text-2xl';
            } else {
                icon.className = 'fas fa-times text-2xl';
            }
        });
    }
});
</script>
