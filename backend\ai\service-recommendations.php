<?php
/**
 * AI Service Recommendations
 * توصيات الخدمات بالذكاء الاصطناعي
 */

require_once '../config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح لك بالوصول']);
    exit;
}

$user = getCurrentUser();

// الحصول على بيانات المستخدم للتحليل
$user_data = getUserAnalysisData($user['id']);

try {
    // الحصول على توصيات الخدمات
    $recommendations = getServiceRecommendations($user_data);
    
    echo json_encode([
        'success' => true,
        'recommendations' => $recommendations
    ]);
    
} catch (Exception $e) {
    logError("Service recommendations error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'حدث خطأ أثناء الحصول على التوصيات'
    ]);
}

/**
 * الحصول على بيانات المستخدم للتحليل
 */
function getUserAnalysisData($user_id) {
    try {
        // الحصول على معلومات المستخدم
        $user = fetchOne("SELECT * FROM users WHERE id = :id", ['id' => $user_id]);
        
        // الحصول على طلبات المستخدم السابقة
        $requests = fetchAll(
            "SELECT sr.*, s.service_name_ar 
             FROM service_requests sr 
             LEFT JOIN services s ON sr.service_id = s.id 
             WHERE sr.user_id = :user_id 
             ORDER BY sr.created_at DESC 
             LIMIT 10",
            ['user_id' => $user_id]
        );
        
        // الحصول على إحصائيات
        $stats = [
            'total_requests' => 0,
            'completed_requests' => 0,
            'total_spent' => 0,
            'most_used_services' => []
        ];
        
        $stats['total_requests'] = fetchOne(
            "SELECT COUNT(*) as count FROM service_requests WHERE user_id = :user_id",
            ['user_id' => $user_id]
        )['count'];
        
        $stats['completed_requests'] = fetchOne(
            "SELECT COUNT(*) as count FROM service_requests WHERE user_id = :user_id AND status = 'completed'",
            ['user_id' => $user_id]
        )['count'];
        
        $total_spent = fetchOne(
            "SELECT SUM(total_amount) as total FROM service_requests WHERE user_id = :user_id AND status = 'completed'",
            ['user_id' => $user_id]
        );
        $stats['total_spent'] = $total_spent['total'] ?? 0;
        
        // الخدمات الأكثر استخداماً
        $stats['most_used_services'] = fetchAll(
            "SELECT s.service_name_ar, COUNT(*) as usage_count 
             FROM service_requests sr 
             LEFT JOIN services s ON sr.service_id = s.id 
             WHERE sr.user_id = :user_id 
             GROUP BY sr.service_id 
             ORDER BY usage_count DESC 
             LIMIT 5",
            ['user_id' => $user_id]
        );
        
        return [
            'user' => $user,
            'requests' => $requests,
            'stats' => $stats
        ];
        
    } catch (Exception $e) {
        logError("User analysis data error: " . $e->getMessage());
        return null;
    }
}

/**
 * الحصول على توصيات الخدمات من الذكاء الاصطناعي
 */
function getServiceRecommendations($user_data) {
    if (!$user_data) {
        return [];
    }
    
    // إعداد البيانات للذكاء الاصطناعي
    $prompt = createRecommendationPrompt($user_data);
    
    // استدعاء OpenRouter API
    $ai_response = callOpenRouterAPI($prompt);
    
    if (!$ai_response) {
        return getDefaultRecommendations($user_data);
    }
    
    // تحليل استجابة الذكاء الاصطناعي
    return parseAIRecommendations($ai_response);
}

/**
 * إنشاء prompt للذكاء الاصطناعي
 */
function createRecommendationPrompt($user_data) {
    $user = $user_data['user'];
    $stats = $user_data['stats'];
    $requests = $user_data['requests'];
    
    $prompt = "أنت مستشار محاسبي خبير. قم بتحليل بيانات العميل التالي واقترح 3-5 خدمات محاسبية مناسبة له:\n\n";
    
    $prompt .= "معلومات العميل:\n";
    $prompt .= "- الاسم: {$user['full_name']}\n";
    $prompt .= "- نوع الشركة: " . ($user['company_name'] ?? 'فردي') . "\n";
    $prompt .= "- الرقم الضريبي: " . ($user['tax_number'] ? 'موجود' : 'غير موجود') . "\n";
    
    $prompt .= "\nإحصائيات الاستخدام:\n";
    $prompt .= "- إجمالي الطلبات: {$stats['total_requests']}\n";
    $prompt .= "- الطلبات المكتملة: {$stats['completed_requests']}\n";
    $prompt .= "- إجمالي المبلغ المنفق: {$stats['total_spent']} ريال\n";
    
    if (!empty($stats['most_used_services'])) {
        $prompt .= "\nالخدمات الأكثر استخداماً:\n";
        foreach ($stats['most_used_services'] as $service) {
            $prompt .= "- {$service['service_name_ar']}: {$service['usage_count']} مرة\n";
        }
    }
    
    if (!empty($requests)) {
        $prompt .= "\nآخر الطلبات:\n";
        foreach (array_slice($requests, 0, 3) as $request) {
            $prompt .= "- {$request['request_title']} ({$request['service_name_ar']})\n";
        }
    }
    
    $prompt .= "\nالخدمات المتاحة:\n";
    $prompt .= "1. حساب الضرائب\n";
    $prompt .= "2. المراجعة والتدقيق المالي\n";
    $prompt .= "3. إعداد القوائم المالية\n";
    $prompt .= "4. تسوية الحسابات المالية\n";
    $prompt .= "5. إدارة الأرباح والضرائب\n";
    $prompt .= "6. إدارة الديون\n";
    $prompt .= "7. خدمات الاستشارة المالية\n";
    
    $prompt .= "\nيرجى تقديم توصيات بالتنسيق التالي:\n";
    $prompt .= "RECOMMENDATION: [اسم الخدمة]\n";
    $prompt .= "REASON: [سبب التوصية]\n";
    $prompt .= "PRIORITY: [عالية/متوسطة/منخفضة]\n";
    $prompt .= "---\n";
    
    $prompt .= "\nاجعل التوصيات باللغة العربية ومناسبة لحالة العميل.";
    
    return $prompt;
}

/**
 * استدعاء OpenRouter API
 */
function callOpenRouterAPI($prompt) {
    $api_key = OPENROUTER_API_KEY;
    $api_url = OPENROUTER_API_URL;
    $model = AI_MODEL;
    
    $data = [
        'model' => $model,
        'messages' => [
            [
                'role' => 'user',
                'content' => $prompt
            ]
        ],
        'max_tokens' => 800,
        'temperature' => 0.7
    ];
    
    $headers = [
        'Authorization: Bearer ' . $api_key,
        'Content-Type: application/json',
        'HTTP-Referer: ' . SITE_URL,
        'X-Title: ' . SITE_NAME
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error || $http_code !== 200) {
        return false;
    }
    
    $result = json_decode($response, true);
    
    if (!$result || !isset($result['choices'][0]['message']['content'])) {
        return false;
    }
    
    return trim($result['choices'][0]['message']['content']);
}

/**
 * تحليل استجابة الذكاء الاصطناعي
 */
function parseAIRecommendations($ai_response) {
    $recommendations = [];
    $sections = explode('---', $ai_response);
    
    foreach ($sections as $section) {
        $lines = explode("\n", trim($section));
        $recommendation = [];
        
        foreach ($lines as $line) {
            if (strpos($line, 'RECOMMENDATION:') === 0) {
                $recommendation['service'] = trim(str_replace('RECOMMENDATION:', '', $line));
            } elseif (strpos($line, 'REASON:') === 0) {
                $recommendation['reason'] = trim(str_replace('REASON:', '', $line));
            } elseif (strpos($line, 'PRIORITY:') === 0) {
                $recommendation['priority'] = trim(str_replace('PRIORITY:', '', $line));
            }
        }
        
        if (!empty($recommendation['service']) && !empty($recommendation['reason'])) {
            $recommendations[] = $recommendation;
        }
    }
    
    return $recommendations;
}

/**
 * توصيات افتراضية في حالة فشل الذكاء الاصطناعي
 */
function getDefaultRecommendations($user_data) {
    $user = $user_data['user'];
    $stats = $user_data['stats'];
    
    $recommendations = [];
    
    // توصيات أساسية حسب حالة المستخدم
    if ($stats['total_requests'] == 0) {
        // مستخدم جديد
        $recommendations[] = [
            'service' => 'حساب الضرائب',
            'reason' => 'خدمة أساسية لجميع الشركات والأفراد',
            'priority' => 'عالية'
        ];
        
        $recommendations[] = [
            'service' => 'إعداد القوائم المالية',
            'reason' => 'ضرورية لتنظيم الوضع المالي',
            'priority' => 'متوسطة'
        ];
    } else {
        // مستخدم لديه طلبات سابقة
        $recommendations[] = [
            'service' => 'المراجعة والتدقيق المالي',
            'reason' => 'مراجعة دورية للتأكد من دقة الحسابات',
            'priority' => 'عالية'
        ];
        
        $recommendations[] = [
            'service' => 'خدمات الاستشارة المالية',
            'reason' => 'تحسين الأداء المالي وتطوير الاستراتيجيات',
            'priority' => 'متوسطة'
        ];
    }
    
    if (!empty($user['tax_number'])) {
        $recommendations[] = [
            'service' => 'إدارة الأرباح والضرائب',
            'reason' => 'تحسين الكفاءة الضريبية للشركة',
            'priority' => 'عالية'
        ];
    }
    
    return $recommendations;
}
?>
