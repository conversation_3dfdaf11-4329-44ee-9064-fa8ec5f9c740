<?php
/**
 * Document Viewer
 * عارض المستندات
 */

require_once 'config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    die('غير مصرح لك بالوصول');
}

$user = getCurrentUser();
$document_id = (int)($_GET['id'] ?? 0);

if (!$document_id) {
    http_response_code(400);
    die('معرف المستند مطلوب');
}

try {
    // الحصول على معلومات المستند
    $sql = "SELECT d.*, sr.user_id as request_user_id, sr.request_title 
            FROM documents d 
            LEFT JOIN service_requests sr ON d.request_id = sr.id 
            WHERE d.id = :id";
    
    $document = fetchOne($sql, ['id' => $document_id]);
    
    if (!$document) {
        http_response_code(404);
        die('المستند غير موجود');
    }
    
    // التحقق من الصلاحيات
    $can_access = false;
    
    if ($user['role'] === 'admin' || $user['role'] === 'accountant') {
        $can_access = true;
    } elseif ($user['role'] === 'client') {
        if ($document['user_id'] == $user['id'] || $document['request_user_id'] == $user['id']) {
            $can_access = true;
        }
    }
    
    if (!$can_access) {
        http_response_code(403);
        die('غير مصرح لك بالوصول لهذا المستند');
    }
    
    // التحقق من وجود الملف
    if (!file_exists($document['file_path'])) {
        http_response_code(404);
        die('الملف غير موجود على الخادم');
    }
    
    // تحديد نوع العرض حسب نوع الملف
    $viewable_types = ['pdf', 'jpg', 'jpeg', 'png', 'gif'];
    $is_viewable = in_array(strtolower($document['file_type']), $viewable_types);
    
    if (!$is_viewable) {
        // إعادة توجيه للتحميل للملفات غير القابلة للعرض
        header('Location: download.php?id=' . $document_id);
        exit;
    }
    
    // تسجيل عملية العرض
    try {
        logError("Document view: User {$user['id']} viewed document {$document_id}");
    } catch (Exception $e) {
        // تجاهل أخطاء التسجيل
    }
    
} catch (Exception $e) {
    logError("Document view error: " . $e->getMessage());
    http_response_code(500);
    die('حدث خطأ أثناء عرض المستند');
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض المستند - <?php echo htmlspecialchars($document['original_filename']); ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'muted-green': '#CAD2C5',
                        'warm-beige': '#FFE5B4',
                        'earth-green': '#52796F',
                        'sand-brown': '#8D7B68',
                        'light-gray': '#F0F0F0',
                    }
                }
            }
        }
    </script>
    
    <style>
        .document-viewer {
            max-height: 80vh;
            overflow: auto;
        }
        
        .pdf-viewer {
            width: 100%;
            height: 80vh;
            border: none;
        }
        
        .image-viewer {
            max-width: 100%;
            max-height: 80vh;
            object-fit: contain;
        }
    </style>
</head>
<body class="font-cairo bg-light-gray">
    
    <div class="min-h-screen">
        <!-- Header -->
        <div class="bg-white shadow-sm border-b">
            <div class="container mx-auto px-4 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <button onclick="window.close()" 
                                class="text-gray-600 hover:text-earth-green transition-colors">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                        <div>
                            <h1 class="text-lg font-semibold text-earth-green">
                                <?php echo htmlspecialchars($document['original_filename']); ?>
                            </h1>
                            <p class="text-sm text-gray-600">
                                <?php echo htmlspecialchars($document['request_title'] ?? 'مستند'); ?> - 
                                <?php echo formatArabicDate($document['upload_date']); ?>
                            </p>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-3 space-x-reverse">
                        <a href="download.php?id=<?php echo $document_id; ?>" 
                           class="bg-earth-green text-white px-4 py-2 rounded-lg hover:bg-sand-brown transition-colors">
                            <i class="fas fa-download ml-2"></i>
                            تحميل
                        </a>
                        
                        <?php if ($document['file_type'] === 'pdf'): ?>
                            <button onclick="printDocument()" 
                                    class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                                <i class="fas fa-print ml-2"></i>
                                طباعة
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Document Viewer -->
        <div class="container mx-auto px-4 py-6">
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="document-viewer">
                    <?php if ($document['file_type'] === 'pdf'): ?>
                        <!-- PDF Viewer -->
                        <iframe src="<?php echo $document['file_path']; ?>" 
                                class="pdf-viewer"
                                id="pdfViewer">
                            <p class="p-6 text-center text-gray-600">
                                متصفحك لا يدعم عرض ملفات PDF. 
                                <a href="download.php?id=<?php echo $document_id; ?>" 
                                   class="text-earth-green hover:text-sand-brown transition-colors">
                                    انقر هنا للتحميل
                                </a>
                            </p>
                        </iframe>
                        
                    <?php elseif (in_array($document['file_type'], ['jpg', 'jpeg', 'png', 'gif'])): ?>
                        <!-- Image Viewer -->
                        <div class="flex justify-center p-6">
                            <img src="<?php echo $document['file_path']; ?>" 
                                 alt="<?php echo htmlspecialchars($document['original_filename']); ?>"
                                 class="image-viewer rounded-lg shadow-md"
                                 onclick="toggleFullscreen(this)">
                        </div>
                        
                        <div class="p-4 bg-gray-50 text-center">
                            <p class="text-sm text-gray-600">
                                انقر على الصورة لعرضها بالحجم الكامل
                            </p>
                        </div>
                        
                    <?php else: ?>
                        <!-- Unsupported File Type -->
                        <div class="p-12 text-center">
                            <i class="fas fa-file text-4xl text-gray-400 mb-4"></i>
                            <h3 class="text-lg font-semibold text-gray-700 mb-2">
                                لا يمكن عرض هذا النوع من الملفات
                            </h3>
                            <p class="text-gray-600 mb-6">
                                نوع الملف: <?php echo strtoupper($document['file_type']); ?>
                            </p>
                            <a href="download.php?id=<?php echo $document_id; ?>" 
                               class="bg-earth-green text-white px-6 py-3 rounded-lg hover:bg-sand-brown transition-colors">
                                <i class="fas fa-download ml-2"></i>
                                تحميل الملف
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Document Info -->
            <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold text-earth-green mb-4">
                    <i class="fas fa-info-circle ml-2"></i>
                    معلومات المستند
                </h3>
                
                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">اسم الملف:</label>
                        <p class="text-sm text-gray-900"><?php echo htmlspecialchars($document['original_filename']); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">نوع الملف:</label>
                        <p class="text-sm text-gray-900"><?php echo strtoupper($document['file_type']); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">حجم الملف:</label>
                        <p class="text-sm text-gray-900"><?php echo formatFileSize($document['file_size']); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">تاريخ الرفع:</label>
                        <p class="text-sm text-gray-900"><?php echo formatArabicDate($document['upload_date']); ?></p>
                    </div>
                    
                    <?php if ($document['request_title']): ?>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-1">الطلب المرتبط:</label>
                            <p class="text-sm text-gray-900"><?php echo htmlspecialchars($document['request_title']); ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($document['ai_analysis']): ?>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-1">تحليل الذكاء الاصطناعي:</label>
                            <div class="bg-blue-50 p-3 rounded-lg">
                                <p class="text-sm text-blue-800"><?php echo htmlspecialchars($document['ai_analysis']); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script>
        // Print PDF function
        function printDocument() {
            const iframe = document.getElementById('pdfViewer');
            if (iframe) {
                iframe.contentWindow.print();
            }
        }
        
        // Toggle fullscreen for images
        function toggleFullscreen(img) {
            if (img.requestFullscreen) {
                img.requestFullscreen();
            } else if (img.webkitRequestFullscreen) {
                img.webkitRequestFullscreen();
            } else if (img.msRequestFullscreen) {
                img.msRequestFullscreen();
            }
        }
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                window.close();
            } else if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                printDocument();
            }
        });
    </script>
</body>
</html>

<?php
// Helper function for file size formatting
function formatFileSize($bytes) {
    if ($bytes == 0) return '0 بايت';
    $k = 1024;
    $sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    $i = floor(log($bytes) / log($k));
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}
?>
