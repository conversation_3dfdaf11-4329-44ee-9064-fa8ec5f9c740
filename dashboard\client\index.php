<?php
/**
 * Client Dashboard
 * لوحة تحكم العميل
 */

require_once '../../backend/config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(getUrl('login.php'));
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'client') {
    redirect(getUrl('login.php'));
}

// إحصائيات العميل
$stats = [
    'total_requests' => 0,
    'pending_requests' => 0,
    'completed_requests' => 0,
    'total_amount' => 0
];

try {
    // إجمالي الطلبات
    $stats['total_requests'] = fetchOne(
        "SELECT COUNT(*) as count FROM service_requests WHERE user_id = :user_id",
        ['user_id' => $user['id']]
    )['count'];
    
    // الطلبات المعلقة
    $stats['pending_requests'] = fetchOne(
        "SELECT COUNT(*) as count FROM service_requests WHERE user_id = :user_id AND status IN ('pending', 'in_progress')",
        ['user_id' => $user['id']]
    )['count'];
    
    // الطلبات المكتملة
    $stats['completed_requests'] = fetchOne(
        "SELECT COUNT(*) as count FROM service_requests WHERE user_id = :user_id AND status = 'completed'",
        ['user_id' => $user['id']]
    )['count'];
    
    // إجمالي المبلغ
    $total_amount_result = fetchOne(
        "SELECT SUM(total_amount) as total FROM service_requests WHERE user_id = :user_id",
        ['user_id' => $user['id']]
    );
    $stats['total_amount'] = $total_amount_result['total'] ?? 0;
    
} catch (Exception $e) {
    logError("Dashboard stats error: " . $e->getMessage());
}

// أحدث الطلبات
$recent_requests = [];
try {
    $recent_requests = fetchAll(
        "SELECT sr.*, s.service_name_ar 
         FROM service_requests sr 
         LEFT JOIN services s ON sr.service_id = s.id 
         WHERE sr.user_id = :user_id 
         ORDER BY sr.created_at DESC 
         LIMIT 5",
        ['user_id' => $user['id']]
    );
} catch (Exception $e) {
    logError("Recent requests error: " . $e->getMessage());
}

// الحصول على رسالة الفلاش
$flash_message = getFlashMessage();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - <?php echo SITE_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'muted-green': '#CAD2C5',
                        'warm-beige': '#FFE5B4',
                        'earth-green': '#52796F',
                        'sand-brown': '#8D7B68',
                        'light-gray': '#F0F0F0',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-cairo bg-light-gray">
    
    <!-- Navigation -->
    <?php include '../../frontend/components/navbar.php'; ?>
    
    <div class="container mx-auto px-4 py-8">
        
        <!-- Flash Messages -->
        <?php if ($flash_message): ?>
            <div class="mb-6 p-4 rounded-lg <?php echo $flash_message['type'] === 'success' ? 'bg-green-100 text-green-700 border border-green-300' : 'bg-red-100 text-red-700 border border-red-300'; ?>">
                <div class="flex items-center">
                    <i class="fas <?php echo $flash_message['type'] === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> ml-2"></i>
                    <?php echo htmlspecialchars($flash_message['message']); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-earth-green mb-2">
                مرحباً، <?php echo htmlspecialchars($user['full_name']); ?>
            </h1>
            <p class="text-gray-600">
                إدارة طلباتك المحاسبية ومتابعة حالتها
            </p>
        </div>
        
        <!-- Statistics Cards -->
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            
            <!-- Total Requests -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">إجمالي الطلبات</p>
                        <p class="text-3xl font-bold text-earth-green"><?php echo $stats['total_requests']; ?></p>
                    </div>
                    <div class="w-12 h-12 bg-earth-green rounded-lg flex items-center justify-center">
                        <i class="fas fa-file-alt text-white text-xl"></i>
                    </div>
                </div>
            </div>
            
            <!-- Pending Requests -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">طلبات معلقة</p>
                        <p class="text-3xl font-bold text-yellow-600"><?php echo $stats['pending_requests']; ?></p>
                    </div>
                    <div class="w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clock text-white text-xl"></i>
                    </div>
                </div>
            </div>
            
            <!-- Completed Requests -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">طلبات مكتملة</p>
                        <p class="text-3xl font-bold text-green-600"><?php echo $stats['completed_requests']; ?></p>
                    </div>
                    <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-white text-xl"></i>
                    </div>
                </div>
            </div>
            
            <!-- Total Amount -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">إجمالي المبلغ</p>
                        <p class="text-3xl font-bold text-sand-brown"><?php echo formatCurrency($stats['total_amount']); ?></p>
                    </div>
                    <div class="w-12 h-12 bg-sand-brown rounded-lg flex items-center justify-center">
                        <i class="fas fa-coins text-white text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <a href="<?php echo getUrl('services/request.php'); ?>" 
               class="bg-earth-green text-white p-4 rounded-lg hover:bg-sand-brown transition-all duration-300 text-center">
                <i class="fas fa-plus-circle text-2xl mb-2"></i>
                <p class="font-semibold">طلب خدمة جديدة</p>
            </a>
            
            <a href="<?php echo getUrl('dashboard/client/requests.php'); ?>" 
               class="bg-warm-beige text-earth-green p-4 rounded-lg hover:bg-opacity-80 transition-all duration-300 text-center">
                <i class="fas fa-list text-2xl mb-2"></i>
                <p class="font-semibold">عرض جميع الطلبات</p>
            </a>
            
            <a href="<?php echo getUrl('dashboard/client/documents.php'); ?>" 
               class="bg-muted-green text-earth-green p-4 rounded-lg hover:bg-opacity-80 transition-all duration-300 text-center">
                <i class="fas fa-folder text-2xl mb-2"></i>
                <p class="font-semibold">إدارة المستندات</p>
            </a>
            
            <a href="<?php echo getUrl('profile.php'); ?>" 
               class="bg-sand-brown text-white p-4 rounded-lg hover:bg-opacity-80 transition-all duration-300 text-center">
                <i class="fas fa-user-cog text-2xl mb-2"></i>
                <p class="font-semibold">إعدادات الحساب</p>
            </a>
        </div>
        
        <!-- Recent Requests -->
        <div class="bg-white rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-semibold text-earth-green">أحدث الطلبات</h2>
                    <a href="<?php echo getUrl('dashboard/client/requests.php'); ?>" 
                       class="text-earth-green hover:text-sand-brown transition-colors">
                        عرض الكل <i class="fas fa-arrow-left mr-2"></i>
                    </a>
                </div>
            </div>
            
            <div class="p-6">
                <?php if (empty($recent_requests)): ?>
                    <div class="text-center py-8">
                        <i class="fas fa-inbox text-4xl text-gray-400 mb-4"></i>
                        <p class="text-gray-600 mb-4">لا توجد طلبات حتى الآن</p>
                        <a href="<?php echo getUrl('services/request.php'); ?>" 
                           class="bg-earth-green text-white px-6 py-2 rounded-lg hover:bg-sand-brown transition-colors">
                            اطلب خدمة الآن
                        </a>
                    </div>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach ($recent_requests as $request): ?>
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <h3 class="font-semibold text-gray-900 mb-1">
                                            <?php echo htmlspecialchars($request['request_title']); ?>
                                        </h3>
                                        <p class="text-sm text-gray-600 mb-2">
                                            <?php echo htmlspecialchars($request['service_name_ar'] ?? 'خدمة محاسبية'); ?>
                                        </p>
                                        <div class="flex items-center space-x-4 space-x-reverse text-xs text-gray-500">
                                            <span>
                                                <i class="fas fa-calendar ml-1"></i>
                                                <?php echo formatArabicDate($request['created_at']); ?>
                                            </span>
                                            <span>
                                                <i class="fas fa-coins ml-1"></i>
                                                <?php echo formatCurrency($request['total_amount']); ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-3 space-x-reverse">
                                        <?php
                                        $status_classes = [
                                            'pending' => 'bg-yellow-100 text-yellow-800',
                                            'in_progress' => 'bg-blue-100 text-blue-800',
                                            'under_review' => 'bg-purple-100 text-purple-800',
                                            'completed' => 'bg-green-100 text-green-800',
                                            'cancelled' => 'bg-red-100 text-red-800'
                                        ];
                                        
                                        $status_text = [
                                            'pending' => 'معلق',
                                            'in_progress' => 'قيد التنفيذ',
                                            'under_review' => 'قيد المراجعة',
                                            'completed' => 'مكتمل',
                                            'cancelled' => 'ملغي'
                                        ];
                                        ?>
                                        <span class="px-3 py-1 rounded-full text-xs font-medium <?php echo $status_classes[$request['status']] ?? 'bg-gray-100 text-gray-800'; ?>">
                                            <?php echo $status_text[$request['status']] ?? $request['status']; ?>
                                        </span>
                                        <a href="<?php echo getUrl('dashboard/client/request-details.php?id=' . $request['id']); ?>" 
                                           class="text-earth-green hover:text-sand-brown transition-colors">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Footer -->
    <?php include '../../frontend/components/footer.php'; ?>
    
    <!-- Scripts -->
    <script src="../../assets/js/main.js"></script>
</body>
</html>
