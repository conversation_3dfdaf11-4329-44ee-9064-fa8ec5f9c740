<?php
/**
 * AI Document Analysis Endpoint
 * نقطة نهاية تحليل المستندات بالذكاء الاصطناعي
 */

require_once 'config/config.php';

// Set JSON response header
header('Content-Type: application/json');

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn()) {
    echo json_encode([
        'success' => false,
        'error' => 'غير مصرح بالوصول'
    ]);
    exit;
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'accountant') {
    echo json_encode([
        'success' => false,
        'error' => 'غير مصرح بالوصول'
    ]);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'error' => 'طريقة طلب غير صحيحة'
    ]);
    exit;
}

// قراءة البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);
$document_id = isset($input['document_id']) ? (int)$input['document_id'] : 0;

if ($document_id <= 0) {
    echo json_encode([
        'success' => false,
        'error' => 'معرف المستند غير صحيح'
    ]);
    exit;
}

try {
    // الحصول على تفاصيل المستند والتحقق من الصلاحيات
    $document = fetchOne(
        "SELECT d.*, sr.assigned_accountant_id, sr.request_title, u.full_name as client_name
         FROM documents d 
         INNER JOIN service_requests sr ON d.request_id = sr.id 
         INNER JOIN users u ON sr.user_id = u.id 
         WHERE d.id = :id AND sr.assigned_accountant_id = :accountant_id",
        ['id' => $document_id, 'accountant_id' => $user['id']]
    );
    
    if (!$document) {
        echo json_encode([
            'success' => false,
            'error' => 'المستند غير موجود أو غير مصرح لك بالوصول إليه'
        ]);
        exit;
    }
    
    // إعداد السياق للذكاء الاصطناعي
    $context = "أنت محاسب خبير متخصص في تحليل المستندات المالية. قم بتحليل المستند التالي وقدم تحليلاً شاملاً يتضمن:
1. نوع المستند وطبيعته
2. البيانات المالية المهمة المستخرجة
3. أي ملاحظات أو تحذيرات محاسبية
4. التوصيات للخطوات التالية

معلومات المستند:
- اسم الملف: {$document['original_filename']}
- نوع الملف: {$document['file_type']}
- حجم الملف: " . number_format($document['file_size'] / 1024, 1) . " KB
- الطلب المرتبط: {$document['request_title']}
- العميل: {$document['client_name']}

يرجى تقديم تحليل مفصل ومفيد باللغة العربية.";
    
    // استدعاء خدمة الذكاء الاصطناعي
    $analysis = callAIService($context, 'document_analysis');
    
    // حفظ التحليل في قاعدة البيانات
    $sql = "UPDATE documents SET ai_analysis = :analysis, is_processed = 1 WHERE id = :id";
    executeQuery($sql, [
        'analysis' => $analysis,
        'id' => $document_id
    ]);
    
    // حفظ المحادثة في سجل الذكاء الاصطناعي
    $conversation_sql = "INSERT INTO ai_conversations (user_id, session_id, message, response, model_used, created_at) 
                        VALUES (:user_id, :session_id, :message, :response, :model_used, NOW())";
    executeQuery($conversation_sql, [
        'user_id' => $user['id'],
        'session_id' => session_id(),
        'message' => "تحليل المستند: {$document['original_filename']}",
        'response' => $analysis,
        'model_used' => getSetting('ai_model', 'deepseek/deepseek-chat:free')
    ]);
    
    echo json_encode([
        'success' => true,
        'analysis' => $analysis
    ]);
    
} catch (Exception $e) {
    logError("AI document analysis error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'حدث خطأ أثناء تحليل المستند'
    ]);
}

/**
 * استدعاء خدمة الذكاء الاصطناعي
 */
function callAIService($prompt, $tool_type) {
    $api_key = getSetting('openrouter_api_key');
    $model = getSetting('ai_model', 'deepseek/deepseek-chat:free');
    
    $data = [
        'model' => $model,
        'messages' => [
            [
                'role' => 'system',
                'content' => 'أنت محاسب خبير متخصص في تحليل المستندات المالية والمحاسبية. قدم تحليلات دقيقة ومفيدة باللغة العربية.'
            ],
            [
                'role' => 'user',
                'content' => $prompt
            ]
        ],
        'max_tokens' => 1500,
        'temperature' => 0.7
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://openrouter.ai/api/v1/chat/completions');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $api_key,
        'HTTP-Referer: ' . SITE_URL,
        'X-Title: ' . SITE_NAME
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code !== 200) {
        throw new Exception('فشل في الاتصال بخدمة الذكاء الاصطناعي');
    }
    
    $result = json_decode($response, true);
    
    if (!isset($result['choices'][0]['message']['content'])) {
        throw new Exception('استجابة غير صحيحة من خدمة الذكاء الاصطناعي');
    }
    
    return $result['choices'][0]['message']['content'];
}
?>
