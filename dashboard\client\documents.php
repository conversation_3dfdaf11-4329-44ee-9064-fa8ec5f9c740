<?php
/**
 * Client Documents Management
 * إدارة مستندات العميل
 */

require_once '../../backend/config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(getUrl('login.php'));
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'client') {
    redirect(getUrl('login.php'));
}

// الحصول على طلبات العميل
$user_requests = [];
try {
    $user_requests = fetchAll(
        "SELECT sr.*, s.service_name_ar 
         FROM service_requests sr 
         LEFT JOIN services s ON sr.service_id = s.id 
         WHERE sr.user_id = :user_id 
         ORDER BY sr.created_at DESC",
        ['user_id' => $user['id']]
    );
} catch (Exception $e) {
    logError("User requests fetch error: " . $e->getMessage());
}

// معالجة رفع المستندات
$error_message = '';
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['upload_document'])) {
    $request_id = (int)($_POST['request_id'] ?? 0);
    
    // التحقق من وجود الطلب وملكيته للمستخدم
    $request = fetchOne(
        "SELECT * FROM service_requests WHERE id = :id AND user_id = :user_id",
        ['id' => $request_id, 'user_id' => $user['id']]
    );
    
    if (!$request) {
        $error_message = 'الطلب غير موجود أو غير مصرح لك بالوصول إليه';
    } elseif (!isset($_FILES['document']) || $_FILES['document']['error'] !== UPLOAD_ERR_OK) {
        $error_message = 'يرجى اختيار ملف للرفع';
    } else {
        $file = $_FILES['document'];
        $file_size = $file['size'];
        $file_name = $file['name'];
        $file_tmp = $file['tmp_name'];
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
        
        // التحقق من نوع الملف
        $allowed_types = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png'];
        if (!in_array($file_ext, $allowed_types)) {
            $error_message = 'نوع الملف غير مدعوم. الأنواع المدعومة: ' . implode(', ', $allowed_types);
        } elseif ($file_size > MAX_FILE_SIZE) {
            $error_message = 'حجم الملف كبير جداً. الحد الأقصى ' . (MAX_FILE_SIZE / 1024 / 1024) . ' ميجابايت';
        } else {
            // إنشاء اسم ملف فريد
            $unique_name = uniqid() . '_' . time() . '.' . $file_ext;
            $upload_path = UPLOAD_DIR . '/' . $unique_name;
            
            // إنشاء مجلد الرفع إذا لم يكن موجوداً
            createDirectoryIfNotExists(UPLOAD_DIR);
            
            if (move_uploaded_file($file_tmp, $upload_path)) {
                try {
                    global $database;
                    
                    // حفظ معلومات الملف في قاعدة البيانات
                    $document_data = [
                        'request_id' => $request_id,
                        'user_id' => $user['id'],
                        'original_filename' => $file_name,
                        'stored_filename' => $unique_name,
                        'file_path' => $upload_path,
                        'file_size' => $file_size,
                        'file_type' => $file_ext
                    ];
                    
                    $document_id = $database->insert('documents', $document_data);
                    
                    if ($document_id) {
                        $success_message = 'تم رفع المستند بنجاح';
                        
                        // إنشاء إشعار للإدارة
                        $notification_data = [
                            'user_id' => 1, // المدير
                            'title' => 'مستند جديد',
                            'message' => "تم رفع مستند جديد للطلب رقم {$request_id}",
                            'type' => 'info',
                            'related_request_id' => $request_id
                        ];
                        $database->insert('notifications', $notification_data);
                    } else {
                        $error_message = 'حدث خطأ أثناء حفظ معلومات الملف';
                        unlink($upload_path); // حذف الملف في حالة الخطأ
                    }
                } catch (Exception $e) {
                    $error_message = 'حدث خطأ أثناء رفع الملف';
                    logError("Document upload error: " . $e->getMessage());
                    if (file_exists($upload_path)) {
                        unlink($upload_path);
                    }
                }
            } else {
                $error_message = 'فشل في رفع الملف. يرجى المحاولة مرة أخرى';
            }
        }
    }
}

// الحصول على مستندات العميل
$user_documents = [];
try {
    $user_documents = fetchAll(
        "SELECT d.*, sr.request_title, s.service_name_ar 
         FROM documents d 
         LEFT JOIN service_requests sr ON d.request_id = sr.id 
         LEFT JOIN services s ON sr.service_id = s.id 
         WHERE d.user_id = :user_id 
         ORDER BY d.upload_date DESC",
        ['user_id' => $user['id']]
    );
} catch (Exception $e) {
    logError("User documents fetch error: " . $e->getMessage());
}

// الحصول على رسالة الفلاش
$flash_message = getFlashMessage();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستندات - <?php echo SITE_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'muted-green': '#CAD2C5',
                        'warm-beige': '#FFE5B4',
                        'earth-green': '#52796F',
                        'sand-brown': '#8D7B68',
                        'light-gray': '#F0F0F0',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-cairo bg-light-gray">
    
    <!-- Navigation -->
    <?php include '../../frontend/components/navbar.php'; ?>
    
    <div class="container mx-auto px-4 py-8">
        
        <!-- Flash Messages -->
        <?php if ($flash_message): ?>
            <div class="mb-6 p-4 rounded-lg <?php echo $flash_message['type'] === 'success' ? 'bg-green-100 text-green-700 border border-green-300' : 'bg-red-100 text-red-700 border border-red-300'; ?>">
                <div class="flex items-center">
                    <i class="fas <?php echo $flash_message['type'] === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> ml-2"></i>
                    <?php echo htmlspecialchars($flash_message['message']); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Header -->
        <div class="mb-8">
            <nav class="flex items-center space-x-2 space-x-reverse text-sm text-gray-600 mb-4">
                <a href="<?php echo getUrl(); ?>" class="hover:text-earth-green transition-colors">الرئيسية</a>
                <i class="fas fa-chevron-left text-xs"></i>
                <a href="<?php echo getUrl('dashboard/client'); ?>" class="hover:text-earth-green transition-colors">لوحة التحكم</a>
                <i class="fas fa-chevron-left text-xs"></i>
                <span class="text-earth-green">إدارة المستندات</span>
            </nav>
            
            <h1 class="text-3xl font-bold text-earth-green mb-2">
                إدارة المستندات
            </h1>
            <p class="text-gray-600">
                رفع وإدارة المستندات المتعلقة بطلباتك المحاسبية
            </p>
        </div>
        
        <div class="grid lg:grid-cols-3 gap-8">
            
            <!-- Upload Form -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-xl font-semibold text-earth-green mb-6">
                        <i class="fas fa-upload ml-2"></i>
                        رفع مستند جديد
                    </h2>
                    
                    <!-- Error Message -->
                    <?php if ($error_message): ?>
                        <div class="mb-4 p-3 rounded-lg bg-red-100 text-red-700 border border-red-300 text-sm">
                            <i class="fas fa-exclamation-circle ml-1"></i>
                            <?php echo htmlspecialchars($error_message); ?>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Success Message -->
                    <?php if ($success_message): ?>
                        <div class="mb-4 p-3 rounded-lg bg-green-100 text-green-700 border border-green-300 text-sm">
                            <i class="fas fa-check-circle ml-1"></i>
                            <?php echo htmlspecialchars($success_message); ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" enctype="multipart/form-data" class="space-y-4">
                        <input type="hidden" name="upload_document" value="1">
                        
                        <!-- Request Selection -->
                        <div>
                            <label for="request_id" class="block text-sm font-medium text-gray-700 mb-2">
                                الطلب المرتبط <span class="text-red-500">*</span>
                            </label>
                            <select id="request_id" 
                                    name="request_id" 
                                    required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors text-sm">
                                <option value="">اختر الطلب</option>
                                <?php foreach ($user_requests as $request): ?>
                                    <option value="<?php echo $request['id']; ?>">
                                        <?php echo htmlspecialchars($request['request_title']); ?> 
                                        (<?php echo htmlspecialchars($request['service_name_ar'] ?? 'خدمة محاسبية'); ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <!-- File Upload -->
                        <div>
                            <label for="document" class="block text-sm font-medium text-gray-700 mb-2">
                                المستند <span class="text-red-500">*</span>
                            </label>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-earth-green transition-colors">
                                <input type="file" 
                                       id="document" 
                                       name="document" 
                                       required
                                       accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"
                                       class="hidden">
                                <label for="document" class="cursor-pointer">
                                    <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-2"></i>
                                    <p class="text-sm text-gray-600">انقر لاختيار ملف أو اسحبه هنا</p>
                                    <p class="text-xs text-gray-500 mt-1">PDF, DOC, XLS, JPG (حتى 10 ميجابايت)</p>
                                </label>
                            </div>
                            <div id="file-info" class="mt-2 text-sm text-gray-600 hidden"></div>
                        </div>
                        
                        <!-- Submit Button -->
                        <button type="submit" 
                                class="w-full bg-earth-green text-white py-2 px-4 rounded-lg font-semibold hover:bg-sand-brown transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-earth-green focus:ring-offset-2">
                            <i class="fas fa-upload ml-2"></i>
                            رفع المستند
                        </button>
                    </form>
                    
                    <!-- Upload Guidelines -->
                    <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                        <h3 class="text-sm font-semibold text-gray-700 mb-2">إرشادات الرفع:</h3>
                        <ul class="text-xs text-gray-600 space-y-1">
                            <li>• الحد الأقصى لحجم الملف: 10 ميجابايت</li>
                            <li>• الأنواع المدعومة: PDF, DOC, XLS, JPG, PNG</li>
                            <li>• تأكد من وضوح المستندات</li>
                            <li>• لا تشارك معلومات حساسة غير ضرورية</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Documents List -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-lg">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-xl font-semibold text-earth-green">
                            <i class="fas fa-folder ml-2"></i>
                            مستنداتي
                        </h2>
                    </div>
                    
                    <div class="p-6">
                        <?php if (empty($user_documents)): ?>
                            <div class="text-center py-8">
                                <i class="fas fa-folder-open text-4xl text-gray-400 mb-4"></i>
                                <p class="text-gray-600 mb-4">لا توجد مستندات مرفوعة حتى الآن</p>
                                <p class="text-sm text-gray-500">ابدأ برفع مستنداتك المحاسبية لتسريع معالجة طلباتك</p>
                            </div>
                        <?php else: ?>
                            <div class="space-y-4">
                                <?php foreach ($user_documents as $document): ?>
                                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-4 space-x-reverse">
                                                <div class="w-12 h-12 bg-earth-green rounded-lg flex items-center justify-center">
                                                    <?php
                                                    $icon_map = [
                                                        'pdf' => 'fa-file-pdf',
                                                        'doc' => 'fa-file-word',
                                                        'docx' => 'fa-file-word',
                                                        'xls' => 'fa-file-excel',
                                                        'xlsx' => 'fa-file-excel',
                                                        'jpg' => 'fa-file-image',
                                                        'jpeg' => 'fa-file-image',
                                                        'png' => 'fa-file-image'
                                                    ];
                                                    $icon = $icon_map[$document['file_type']] ?? 'fa-file';
                                                    ?>
                                                    <i class="fas <?php echo $icon; ?> text-white"></i>
                                                </div>
                                                <div>
                                                    <h3 class="font-semibold text-gray-900">
                                                        <?php echo htmlspecialchars($document['original_filename']); ?>
                                                    </h3>
                                                    <p class="text-sm text-gray-600">
                                                        <?php echo htmlspecialchars($document['request_title']); ?>
                                                    </p>
                                                    <div class="flex items-center space-x-4 space-x-reverse text-xs text-gray-500 mt-1">
                                                        <span>
                                                            <i class="fas fa-calendar ml-1"></i>
                                                            <?php echo formatArabicDate($document['upload_date']); ?>
                                                        </span>
                                                        <span>
                                                            <i class="fas fa-hdd ml-1"></i>
                                                            <?php echo formatFileSize($document['file_size']); ?>
                                                        </span>
                                                        <?php if ($document['is_processed']): ?>
                                                            <span class="text-green-600">
                                                                <i class="fas fa-check-circle ml-1"></i>
                                                                تم المعالجة
                                                            </span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex items-center space-x-2 space-x-reverse">
                                                <a href="<?php echo getUrl('backend/download.php?id=' . $document['id']); ?>" 
                                                   class="text-earth-green hover:text-sand-brown transition-colors"
                                                   title="تحميل">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                                <button onclick="viewDocument(<?php echo $document['id']; ?>)" 
                                                        class="text-blue-600 hover:text-blue-800 transition-colors"
                                                        title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <?php if ($document['ai_analysis']): ?>
                                            <div class="mt-3 p-3 bg-blue-50 rounded-lg">
                                                <h4 class="text-sm font-semibold text-blue-800 mb-1">
                                                    <i class="fas fa-robot ml-1"></i>
                                                    تحليل الذكاء الاصطناعي:
                                                </h4>
                                                <p class="text-sm text-blue-700">
                                                    <?php echo htmlspecialchars($document['ai_analysis']); ?>
                                                </p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Footer -->
    <?php include '../../frontend/components/footer.php'; ?>
    
    <!-- Scripts -->
    <script src="../../assets/js/main.js"></script>
    
    <script>
        // File upload preview
        document.getElementById('document').addEventListener('change', function() {
            const fileInfo = document.getElementById('file-info');
            const file = this.files[0];
            
            if (file) {
                const size = (file.size / 1024 / 1024).toFixed(2);
                fileInfo.innerHTML = `
                    <i class="fas fa-file ml-1"></i>
                    ${file.name} (${size} ميجابايت)
                `;
                fileInfo.classList.remove('hidden');
            } else {
                fileInfo.classList.add('hidden');
            }
        });
        
        // View document function
        function viewDocument(documentId) {
            // Open document in new window/tab
            window.open(`<?php echo getUrl('backend/view-document.php?id='); ?>${documentId}`, '_blank');
        }
        
        // Format file size function
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 بايت';
            const k = 1024;
            const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>

<?php
// Helper function for file size formatting
function formatFileSize($bytes) {
    if ($bytes == 0) return '0 بايت';
    $k = 1024;
    $sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    $i = floor(log($bytes) / log($k));
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}
?>
